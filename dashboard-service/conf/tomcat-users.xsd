<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<xs:schema xmlns="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://tomcat.apache.org/xml"
           xmlns:users="http://tomcat.apache.org/xml"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           version="1.0">
  <xs:element name="tomcat-users">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="role">
          <xs:complexType>
            <xs:attribute name="rolename" use="required" type="users:entityname" />
            <xs:attribute name="description" type="xs:string" />
          </xs:complexType>
        </xs:element>
        <xs:element name="group">
          <xs:complexType>
            <xs:attribute name="groupname" use="required" type="users:entityname" />
            <xs:attribute name="description" type="xs:string" />
            <xs:attribute name="roles" type="xs:string" />
          </xs:complexType>
        </xs:element>
        <xs:element name="user">
          <xs:complexType>
            <xs:attribute name="username" use="required" type="users:entityname" />
            <xs:attribute name="fullname" type="xs:string" />
            <xs:attribute name="password" type="xs:string" />
            <xs:attribute name="roles" type="xs:string" />
            <xs:attribute name="groups" type="xs:string" />
          </xs:complexType>
        </xs:element>
      </xs:choice>
      <xs:attribute name="version" type="xs:string" />
    </xs:complexType>
  </xs:element>
  <xs:simpleType name="entityname">
    <xs:restriction base="xs:string">
      <xs:minLength value="1"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>
