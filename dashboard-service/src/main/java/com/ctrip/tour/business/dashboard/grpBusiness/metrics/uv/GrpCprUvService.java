package com.ctrip.tour.business.dashboard.grpBusiness.metrics.uv;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.EdwLogGrpCprPlatformFlowCrDiDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.google.common.base.Joiner;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 * 自服务覆盖率
 */
@Service
@IndexAssemblyHandler(calcDateName = "partition_d",
        calcFieldName = "result_value",
        tableName = "edw_log_grp_cpr_platform_flow_cr_di")
@Slf4j
public class GrpCprUvService extends IndexCommonQueryAbstractSerice {

    @Autowired
    EdwLogGrpCprPlatformFlowCrDiDao flowCrDiDao;

    public GrpCprUvService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.SELF_SERVICE_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = "  count(distinct concat(vid,partition_d)) as result_value ";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = flowCrDiDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query cdm_sev_grp_cpr_platform_self_srv_cr_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        Map<String, Object> result;
        if (rowData != null && !rowData.isEmpty()) {
            result = rowData.getFirst();
            Long resultValue = (Long) result.get("result_value");
            result.put("result_value", BigDecimal.valueOf(resultValue));
        }
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
