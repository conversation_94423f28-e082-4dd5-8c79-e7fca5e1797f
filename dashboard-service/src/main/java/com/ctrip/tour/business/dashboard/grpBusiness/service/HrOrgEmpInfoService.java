package com.ctrip.tour.business.dashboard.grpBusiness.service;

import com.ctrip.soa._24922.GetGrpOrgTabListResponseType;
import com.ctrip.soa._24922.GrpEmployeeItem;
import com.ctrip.soa._24922.GrpOrganizationNode;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.CustRegionOrgInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrgTreeVacationDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.EdwHrEmpVacationDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.GrpCustTourAsseRegionInfoDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrgTreeVacation;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HrOrgEmpInfoService {

    @Autowired
    DimOrgTreeVacationDao dimOrgTreeVacationDao;
    @Autowired
    EdwHrEmpVacationDao edwHrEmpVacationDao;
    @Autowired
    UserInfoService userInfoService;
    @Autowired
    GrpCustTourAsseRegionInfoDao grpCustTourAsseRegionInfoDao;
    @Autowired
    CustEmpOrgInfoService custEmpOrgInfoService;
    @Autowired
    DepTreeCache depTreeCache;

    private static final String TOP_LEADER_CODE = "S00546";
    private static final String CUST_LEADER_CODE = "S43455";
    private static final String GRP_LEADER_CODE = "S40160";
    private static final String PRIVATE_LEADER_CODE = "S08994";
    private static final String DEP_LEADER_CODE = "S40160";


    //判断是否为跟团的员工
    public boolean isGrpEmployee(String empCode) throws SQLException {

        //查询该员工信息
        EdwHrEmpVacation edwHrEmpVacation = edwHrEmpVacationDao.queryByEmpCode(empCode);

        return edwHrEmpVacation != null;

    }

    public EdwHrEmpVacation queryEmpInfoByCode(String empCode) throws SQLException {
        return edwHrEmpVacationDao.queryByEmpCode(empCode);
    }


    public GetGrpOrgTabListResponseType getGrpOrgTabList() throws Exception {

        GetGrpOrgTabListResponseType responseType = new GetGrpOrgTabListResponseType();
        List<Integer> tablList = Lists.newArrayList();
        int defaultTab = 1;
        String mappingEmpCode = userInfoService.getMappingEmpCode();
        if (StringUtils.equalsIgnoreCase(mappingEmpCode, TOP_LEADER_CODE)) {
            tablList.add(1);
            tablList.add(2);
            tablList.add(3);
        } else if (StringUtils.equalsIgnoreCase(mappingEmpCode, DEP_LEADER_CODE)) {
            tablList.add(1);
            tablList.add(2);
            tablList.add(3);
            defaultTab = 1;
        } else if (StringUtils.equalsIgnoreCase(mappingEmpCode, GRP_LEADER_CODE)) {
            tablList.add(1);
            defaultTab = 1;
        } else {
            int defaultTab4NomalEmp = getDefaultTab4NomalEmp(mappingEmpCode);
            tablList.add(defaultTab4NomalEmp);
            defaultTab = defaultTab4NomalEmp;
        }
        responseType.setTabList(tablList);
        responseType.setDefaultTabList(defaultTab);
        return responseType;
    }


    public GetGrpOrgTabListResponseType getGrpOrgTabListV2() throws Exception {
        depTreeCache.initDepTreeSync();
        GetGrpOrgTabListResponseType responseType = new GetGrpOrgTabListResponseType();
        responseType.setTabList(new ArrayList<>());
        String mappingEmpCode = userInfoService.getMappingEmpCode();
        // 所在的部门，或管理的部门，包含了几个组织
        List<String> depts = depTreeCache.getDeptByEmpCode(mappingEmpCode);
        // 跟团游
        boolean genTuanYou = false;
        for (String dept : depts) {
            genTuanYou = depTreeCache.isSubDept("SO010001", dept) || genTuanYou;
        }
        if (genTuanYou) {
            responseType.getTabList().add(100);
        }

        // 私家团
        boolean siJiaTuan = false;
        for (String dept : depts) {
            siJiaTuan = depTreeCache.isSubDept("SO010061", dept) || siJiaTuan;
        }
        if (siJiaTuan) {
            responseType.getTabList().add(210);
        }

        // 拼小团
        boolean pinXiaoTuan = false;
        for (String dept : depts) {
            pinXiaoTuan = depTreeCache.isSubDept("SO013040", dept) || pinXiaoTuan;
        }
        if (pinXiaoTuan) {
            responseType.getTabList().add(210);
            responseType.getTabList().add(220);
        }

        // 定制游
        boolean dingZhiYou = false;
        for (String dept : depts) {

            dingZhiYou = depTreeCache.isSubDept("53052", dept) || dingZhiYou;
        }
        EdwHrEmpVacation emp = depTreeCache.getEmpByEmpID(mappingEmpCode);
        if ((emp != null && emp.getOrgIdPath() != null)
                && (emp.getOrgNamePath().contains("Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>团队游业务部>独立出游>定制游") //NOSONAR
                || "Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>团队游业务部>独立出游>定制游".contains(emp.getOrgNamePath()))) { //NOSONAR
            dingZhiYou = true;
        }
        if (dingZhiYou) {
            responseType.getTabList().add(230);
        }

        // 去重
        responseType.setTabList(responseType.getTabList().stream().distinct().collect(Collectors.toList()));

        // default
        if (responseType.getTabList() != null && !responseType.getTabList().isEmpty()) {
            responseType.setDefaultTabList(responseType.getTabList().get(0));
        }


        return responseType;
    }

    private int getDefaultTab4NomalEmp(String empCode) throws Exception {
        EdwHrEmpVacation edwHrEmpVacation = edwHrEmpVacationDao.queryByEmpCode(empCode);
        String teamId = edwHrEmpVacation.getTeamId();
        String teamId2 = edwHrEmpVacation.getTeamId2();
        String teamId1 = edwHrEmpVacation.getTeamId1();

        if (StringUtils.equals(teamId, "SO010001") || StringUtils.equals(teamId1, "SO010001")) {
            return 1;
        } else if ((StringUtils.equals(teamId1, "SO011085") || StringUtils.equals(teamId1, "SO013040")) && !StringUtils.equals("S43455", empCode)) {
            return 2;
        } else {
            List<CustRegionOrgInfoDTO> custTourRegionInfos = custEmpOrgInfoService.getCustOrgRegionList(empCode, "", "");
            if (CollectionUtils.isNotEmpty(custTourRegionInfos) || StringUtils.equals(teamId2, "SO011086")) {
                return 3;
            }
        }
        return -1;
    }

    public List<GrpOrganizationNode> getOrgTree(String queryEmpCode, Integer selectTab) throws Exception {
        List<GrpOrganizationNode> organizationItemList = new ArrayList<>();
        String empCode = replaceEmpCode(queryEmpCode, selectTab);
        //查询该员工信息
        EdwHrEmpVacation edwHrEmpVacation = edwHrEmpVacationDao.queryByEmpCode(empCode);
//        if (StringUtils.equals(empCode, GRP_LEADER_CODE)) {
//            edwHrEmpVacation.setTeamId("SO010001");
//            edwHrEmpVacation.setTeamCname("跟团游");
//        }

        //如果人事架构里不存在或者人事架构就是定制游的员工则走定制游架构获取逻辑
        if (Objects.isNull(edwHrEmpVacation) || StringUtils.equals("定制游", edwHrEmpVacation.getTeamCname2())) {//NOSONAR
            List<CustRegionOrgInfoDTO> custTourRegionInfos = custEmpOrgInfoService.getCustOrgRegionList(empCode, "", "");
            if (CollectionUtils.isEmpty(custTourRegionInfos)) {
                return Lists.newArrayList();
            } else {
                GrpOrganizationNode rootOrgNode = new GrpOrganizationNode();
                rootOrgNode.setOrgId(edwHrEmpVacation.getTeamId2());
                rootOrgNode.setOrgName(edwHrEmpVacation.getTeamCname2());

                List<GrpOrganizationNode> collect = custTourRegionInfos.stream().map(ctri -> {
                    GrpOrganizationNode childNode = new GrpOrganizationNode();
                    childNode.setOrgId(ctri.getRegionId().toString());
                    childNode.setOrgName(ctri.getRegionName());
                    childNode.setChildren(ctri.getAreaInfos().stream().map(ctriArea -> {
                        GrpOrganizationNode areaNode = new GrpOrganizationNode();
                        areaNode.setOrgId(ctriArea.getAreaId().toString());
                        areaNode.setOrgName(ctriArea.getAreaName());
                        return areaNode;
                    }).collect(Collectors.toList()));
                    return childNode;
                }).collect(Collectors.toList());
                rootOrgNode.setChildren(collect);
                return Lists.newArrayList(rootOrgNode);
            }
        }

        //组织树的根节点
        GrpOrganizationNode rootOrgNode = new GrpOrganizationNode();
        rootOrgNode.setOrgId(edwHrEmpVacation.getTeamId());
        rootOrgNode.setOrgName(edwHrEmpVacation.getTeamCname());
        organizationItemList.add(rootOrgNode);

        //查找所有以当前员工为领导的组织
        List<DimOrgTreeVacation> childOrganizationInfoList = dimOrgTreeVacationDao.queryByLeaderEmpCode(empCode);

        //不存在以当前员工为领导的组织, 说明他是底层打工人
        if (CollectionUtils.isEmpty(childOrganizationInfoList)) {
            return organizationItemList;
        }

        List<String> childOrgIdList = childOrganizationInfoList.stream()
                .map(DimOrgTreeVacation::getNodeOrgId)
                .filter(nodeOrgId -> (!StringUtils.equals(GRP_LEADER_CODE, empCode) && !StringUtils.equals(TOP_LEADER_CODE, empCode)) || (Objects.equals(selectTab, 1) && StringUtils.equals("SO010001", nodeOrgId)) ||
                        (Objects.equals(selectTab, 2) && StringUtils.equals("SO011085", nodeOrgId)))
                .collect(Collectors.toList());


        //查找所有父级组织id为当前传入id的组织
        List<DimOrgTreeVacation> organizationInfoList = dimOrgTreeVacationDao.queryWithChildreOrgId(childOrgIdList);

        Map<String, List<GrpOrganizationNode>> grpOrgNodeListMap = new HashMap<>();
        for (DimOrgTreeVacation orgInfo : organizationInfoList) {
            String nodeOrgId = orgInfo.getNodeOrgId();
            String parentOrgId = orgInfo.getParentOrgId();
            List<GrpOrganizationNode> grpOrgNodeList = grpOrgNodeListMap.getOrDefault(parentOrgId, new ArrayList<>());
            GrpOrganizationNode node = new GrpOrganizationNode();
            node.setOrgId(nodeOrgId);
            node.setOrgName(orgInfo.getNodeOrgName());
            grpOrgNodeList.add(node);
            grpOrgNodeListMap.put(parentOrgId, grpOrgNodeList);
        }

        //  GrpOrganizationNode smallGroupOrg = new GrpOrganizationNode();
        //  smallGroupOrg.setOrgId("SO013032");
        //  smallGroupOrg.setOrgName("拼小团");//NOSONAR
        //  grpOrgNodeListMap.put("SO013040", Lists.newArrayList(smallGroupOrg));

        //创建队列
        Deque<GrpOrganizationNode> deque = new LinkedList<>();
        deque.addLast(rootOrgNode);
        while (!deque.isEmpty()) {
            GrpOrganizationNode firstNode = deque.pollFirst();
            String firstOrgId = firstNode.getOrgId();

            List<GrpOrganizationNode> grpOrgNodeList = grpOrgNodeListMap.get(firstOrgId);
            if (!GeneralUtil.isEmpty(grpOrgNodeList)) {
                //  if (StringUtils.equals("SO011085", firstOrgId)) {
                //      GrpOrganizationNode smallGroupPOrg = new GrpOrganizationNode();
                //      smallGroupPOrg.setOrgId("SO013040");
                //      smallGroupPOrg.setOrgName("拼小团");//NOSONAR
                //      grpOrgNodeList.add(smallGroupPOrg);
                //  }
                firstNode.setChildren(grpOrgNodeList);
                for (GrpOrganizationNode node : grpOrgNodeList) {
                    deque.addLast(node);
                }
            }
        }

        return organizationItemList;
    }

    private String replaceEmpCode(String originEmpCode, Integer selectTab) {
        if (StringUtils.equalsIgnoreCase(originEmpCode, TOP_LEADER_CODE)) {
            if (Objects.equals(1, selectTab) || Objects.isNull(selectTab)) {
                return GRP_LEADER_CODE;
            } else if (Objects.equals(2, selectTab)) {
                return PRIVATE_LEADER_CODE;
            } else {
                return CUST_LEADER_CODE;
            }
        } else if (StringUtils.equalsIgnoreCase(originEmpCode, DEP_LEADER_CODE)) {
            if (Objects.equals(1, selectTab) || Objects.isNull(selectTab)) {
                return GRP_LEADER_CODE;
            } else if (Objects.equals(3, selectTab)) {
                return CUST_LEADER_CODE;
            } else {
                return PRIVATE_LEADER_CODE;
            }
        }
        return originEmpCode;
    }

    //查询该组织节点下的员工list
    public List<GrpEmployeeItem> getGrpEmpListByOrgId(String orgId) throws Exception {
        return getGrpEmpListByOrgId(orgId, "");
    }

    //
    public EdwHrEmpVacation queryEmpByDomainName(String domainName) throws SQLException {
        List<EdwHrEmpVacation> edwHrEmpVacations = edwHrEmpVacationDao.queryEmpByDomainName(domainName);
        if (CollectionUtils.isNotEmpty(edwHrEmpVacations)) {
            return edwHrEmpVacations.get(0);
        }
        return null;
    }

    //
    public List<EdwHrEmpVacation> queryEmpByDomainNames(List<String> domainNames) throws SQLException {
        List<EdwHrEmpVacation> edwHrEmpVacations = edwHrEmpVacationDao.queryEmpByDomainNames(domainNames);
        return edwHrEmpVacations;
    }

    public List<GrpEmployeeItem> getGrpEmpListByOrgId(String orgId, String displayName) throws Exception {
        if (StringUtils.isBlank(displayName)) {
            displayName = "";
        }

        List<GrpEmployeeItem> custEmployeeList = custEmpOrgInfoService.getCustEmployeeList(orgId, displayName);

        if (CollectionUtils.isNotEmpty(custEmployeeList)) {
            return custEmployeeList;
        }


        List<EdwHrEmpVacation> edwHrEmpVacationList = edwHrEmpVacationDao.queryTeamEmpByDisplayName(orgId, displayName);

        List<GrpEmployeeItem> employeeItemList = new ArrayList<>();
        for (EdwHrEmpVacation bean : edwHrEmpVacationList) {
            GrpEmployeeItem grpEmployeeItem = new GrpEmployeeItem();
            grpEmployeeItem.setEmpCode(bean.getEmpCode());
            grpEmployeeItem.setDisplayName(bean.getDisplayName());
            grpEmployeeItem.setDomainName(bean.getDomainName());
            grpEmployeeItem.setOrgId(bean.getTeamId());
            grpEmployeeItem.setOrgName(bean.getTeamCname());
            grpEmployeeItem.setBusinessLine(checkBusinessLine(bean));
            grpEmployeeItem.setDefaultBusinessLine(getDefaultBusinessLine(bean));
            employeeItemList.add(grpEmployeeItem);
        }
        //把自己排在最前面
        String empCode = userInfoService.getMappingEmpCode();
        if (StringUtils.isNotBlank(empCode)) {
            employeeItemList.sort(Comparator.comparing(item -> empCode.equals(item.getEmpCode()) ? 0 : 1));
        }

        return employeeItemList;
    }

    public List<Integer> checkBusinessLine(EdwHrEmpVacation bean) {

        //0-全部; 1-跟团游; 2-私家团
        switch (bean.getEmpCode()) {
            case "S00546":
                //李小林（度假ceo）
                return Arrays.asList(0, 1, 2);
            case "S40160":
                //肖吟元（跟团游负责人）
                return Collections.singletonList(1);
            case "S08994":
                //周舟（私家团负责人）
                return Collections.singletonList(2);
            case "S43455":
                return Collections.singletonList(3);
            default:
                switch (bean.getTeamId1()) {
                    case "SO010001":
                        //跟团游
                        return Collections.singletonList(1);
                    case "SO011085":
                        //私家团
                        return Collections.singletonList(2);
                    default:
                        return null;
                }
        }
    }

    public Integer getDefaultBusinessLine(EdwHrEmpVacation bean) {

        //0-全部; 1-跟团游; 2-私家团
        switch (bean.getEmpCode()) {
            case "S00546":
                //李小林（度假ceo）
                return 1;
            case "S40160":
                //肖吟元（跟团游负责人）
                return 1;
            case "S08994":
                return 2;
            case "S43455":
                return 3;
            default:
                switch (bean.getTeamId1()) {
                    case "SO010001":
                        //跟团游
                        return 1;
                    case "SO011085":
                        return 2;
                    case "DIYTEAM":
                        return 3;
                    default:
                        return null;
                }
        }
    }


    //查询该员工的所有下属员工的工号
    public List<String> getAllSubordinateEmpCode(String empCode) throws SQLException {
        EdwHrEmpVacation selfEmpInfo = edwHrEmpVacationDao.queryByEmpCode(empCode);
        if (Objects.isNull(selfEmpInfo)) {
            //查不到这个员工
            return new ArrayList<>();
        }

        List<DimOrgTreeVacation> organizationInfoList = dimOrgTreeVacationDao.queryByLeaderEmpCode(empCode);
        List<String> nodeOrgIdList = organizationInfoList.stream().map(DimOrgTreeVacation::getNodeOrgId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nodeOrgIdList)) {
            //纯打工人,没有下属, 只返回他自己
            return Collections.singletonList(empCode);
        } else {
            List<EdwHrEmpVacation> allSubordinateEmpInfoList = edwHrEmpVacationDao.getAllSubordinateEmp(nodeOrgIdList);
            //返回他自己及其所有下属的工号
            List<String> empCodeList = allSubordinateEmpInfoList.stream().map(EdwHrEmpVacation::getEmpCode).collect(Collectors.toList());
            empCodeList.add(empCode);
            return empCodeList;
        }

    }


    public List<GrpOrganizationNode> searchOrgNode(List<GrpOrganizationNode> grpOrganizationNodeList, String searchKey) throws SQLException {

        //根据searchKey匹配组织树中各节点的组织名称
        List<GrpOrganizationNode> result = new ArrayList<>();
        for (GrpOrganizationNode node : grpOrganizationNodeList) {
            if (node.getOrgName().contains(searchKey)) {
                result.add(node);
            }
            if (!GeneralUtil.isEmpty(node.getChildren())) {
                result.addAll(searchOrgNode(node.getChildren(), searchKey));
            }
        }
        return result;
    }

//    public void setSubBuTypeIntoEmp(List<GrpEmployeeItem> employeeItemList) throws SQLException {
//        if(CollectionUtils.isEmpty(employeeItemList)){
//            return;
//        }
//        List<String> empCodeList = employeeItemList.stream().map(GrpEmployeeItem::getEmpCode).collect(Collectors.toList());
//
//         = edwHrEmpVacationDao.batchQueryByEmpCode(empCodeList);
//    }
//
//    public Integer checkSubBuTypeByEmpCode(String empCode) throws SQLException {
//
//        //李小林
//        if("S00546".equals(empCode)){
//            return 0;
//        }
//        //Mac Xiao （肖吟元）
//        if("S40160".equals(empCode)){
//            return 1;
//        }
//
//        edwHrEmpVacationDao.
//    }


}
