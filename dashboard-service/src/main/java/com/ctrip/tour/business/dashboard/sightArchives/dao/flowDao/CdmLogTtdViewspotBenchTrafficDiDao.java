package com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao;

import com.ctrip.soa._24922.FlowConversionFunnel;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CdmLogTtdViewspotBenchTrafficDiDao {
    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    //流量漏斗  数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    //dw_ticketdb.cdm_log_ttd_viewspot_bench_traffic_di
    public FlowConversionFunnel queryFlowConversionFunnel(Long sightId, String startDate, String endDate, Boolean needSubSight, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        //流量转化漏斗
        StringBuilder sql = new StringBuilder("select cast(sum(dtl_uv) as Integer) as dtl_uv," +
                "cast(sum(ord_uv) as Integer) as ord_uv," +
                "cast(sum(ord_cnt) as Integer) as ord_cnt," +
                "sum(ord_uv)/sum(dtl_uv) as orderConversionRate," +
                "sum(ord_cnt)/sum(ord_uv) as transactionConversionRate" +
                " from cdm_log_ttd_viewspot_bench_traffic_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new FlowConversionFunnel();
        }
        FlowConversionFunnel flowConversionFunnel = new FlowConversionFunnel();
        flowConversionFunnel.setDetailPageUv((Integer) result.get(0).get("dtl_uv"));
        flowConversionFunnel.setOrderPageUv((Integer) result.get(0).get("ord_uv"));
        flowConversionFunnel.setSubmitOrderCount((Integer) result.get(0).get("ord_cnt"));
        flowConversionFunnel.setOrderConversionRate((Double) result.get(0).get("orderConversionRate"));
        flowConversionFunnel.setTransactionConversionRate((Double) result.get(0).get("transactionConversionRate"));
        return flowConversionFunnel;

    }
    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspot_id = ?");
        }else {
            sql.append(" where sub_viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){

        sql.append(" and d between ? and ?");

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    private void appendIsDomestic(List<PreparedParameterBean> parameters, StringBuilder sql, Long isDomestic) {
        sql.append(" and t3.is_domestic = ? ");
        parameters.add(new PreparedParameterBean(String.valueOf(isDomestic), Types.INTEGER));
    }

    public List<Map<String,Object>> queryFlowMetricTrendLine(Long sightId, String startDate, String endDate, Boolean needSubSight, List<Integer> high, List<Integer> low) {
        //趋势线
        StringBuilder sql = new StringBuilder("select DATE_FORMAT(STR_TO_DATE(d, '%Y-%m-%d'), '%Y%m') as date_time," +
                "cast(sum(dtl_uv) as Integer) as dtl_uv," +
                "sum(ord_cnt)/sum(dtl_uv) as conversionRate" +
                " from cdm_log_ttd_viewspot_bench_traffic_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and d BETWEEN DATE_SUB(CURRENT_DATE, INTERVAL 11 MONTH) AND CURRENT_DATE");
        sql.append(" group by date_time");
        sql.append(" order by date_time asc");

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if(result.size() == 0){
            return new ArrayList<>();
        }
        return result;
    }




    public List<Map<String, Object>> queryUserAttraction(List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> res = new ArrayList<>();
        //景点排名
        StringBuilder sql = new StringBuilder("select sub_viewspot_id,sub_viewspot_name," +
                "cast(sum(ord_cnt)/sum(dtl_uv) as Double) as userAttraction from cdm_log_ttd_viewspot_bench_traffic_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendDateRange(parameters, sql,1 ,startDate, endDate);
        sql.append(" group by sub_viewspot_id,sub_viewspot_name");
        sql.append(" order by userAttraction desc");
        sql.append(" limit 10");
        List<Map<String,Object>> UserAttraction = new ArrayList<>();
        try {
            UserAttraction = tktStarRocksDao.getListResultNew(sql.toString(), parameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return UserAttraction;
    }

    private void appendSightIdOfCompetitiveSight(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> competitiveSightIdList) {
        if(CollectionUtils.isNotEmpty(competitiveSightIdList)){
            sql.append(" where sub_viewspot_id in (");
            for(int i = 0; i < competitiveSightIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(competitiveSightIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }else {
            sql.append(" where sub_viewspot_id = 0");
        }
    }

    public Map<String, Object> queryRadarUV(Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType,Boolean needSubSight) {
        //雷达图
        StringBuilder sql = new StringBuilder("select cast(sum(dtl_uv) as Integer) as uv" +
                " from cdm_log_ttd_viewspot_bench_traffic_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    public Map<String, Object> queryRadarUVPop(Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType,Boolean needSubSight) {
        //雷达图
        StringBuilder sql = new StringBuilder("select cast(sum(dtl_uv) as Double) as uv" +
                " from cdm_log_ttd_viewspot_bench_traffic_di t1 inner join v_dim_date t2");

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    private void appendJoinON(List<PreparedParameterBean> lyparameters, StringBuilder lysql, Integer dateType) {
        if(dateType == 2){
            lysql.append(" on t1.d = t2.date_lastyear");
        }else {
            lysql.append(" on t1.d = t2.date_lastyear");
        }
    }

    private void appendJoinONScenicInfo(StringBuilder lysql) {
        lysql.append(" inner join dim_prd_tkt_scenic_info_core t3 on t1.viewspot_id = t3.viewspot_id ");
    }

    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, Integer dateType, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }

//    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
//        if(businessType != 1){
//            sql.append(" and bu_type = ?");
//            parameters.add(new PreparedParameterBean(businessType==2?"tkt":"act", Types.VARCHAR));
//        }
//
//    }
//    //拼供应商id列表
//    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
//        if(CollectionUtils.isNotEmpty(vendorIdList)){
//            sql.append(" and vend_id in (");
//            for(int i = 0; i < vendorIdList.size(); i++){
//                if(i == 0){
//                    sql.append("?");
//                }else {
//                    sql.append(",?");
//                }
//                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
//            }
//            sql.append(")");
//        }
//    }

    public Map<String, Object> queryRadarUVAverage(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Boolean need, Long isDomestic) {
        //雷达图 cast(sum(ord_ttd_suc_income) as Double)/COUNT(DISTINCT vst_id)
        StringBuilder sql = new StringBuilder("select cast(sum(dtl_uv) as Varchar) as uv_average" +
                " from cdm_log_ttd_viewspot_bench_traffic_di t1 ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinONScenicInfo(sql);//关联景点信息表过滤国内\海外景点
        sql.append(" where 1=1 ");

        sql.append(" and t1.d between ? and ?");
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        sql.append(" and t3.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        appendIsDomestic(parameters, sql, isDomestic);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    public Map<String, Object> queryRadarUVAveragePop(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Boolean need, Long isDomestic) {
        //雷达图
        StringBuilder sql = new StringBuilder("select cast(sum(dtl_uv) as Varchar) as uv_average" +
                " from cdm_log_ttd_viewspot_bench_traffic_di t1 inner join v_dim_date t2 ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendJoinONScenicInfo(sql);//关联景点信息表过滤国内\海外景点
        sql.append(" where 1=1 ");
        sql.append(" and t3.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendIsDomestic(parameters, sql, isDomestic);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    //数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    //流量漏斗

    //趋势线  按月聚合
}
