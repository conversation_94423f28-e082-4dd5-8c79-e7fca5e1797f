package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.bean.SaleUnitInfo;
import com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao.*;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesPieChartEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GargleTranslateServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.TtdProductBasicServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.CommonService;
import com.ctrip.tour.business.dashboard.sightArchives.service.SalesService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.NumberFormatUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SalesServiceImpl implements SalesService {

    @Autowired
    CdmOrdTtdVstArchiveDfDao cdmOrdTtdVstArchiveDfDao;
    @Autowired
    AdmPrmVacViewspotDistributionProvinceDetailDfDao admPrmVacViewspotDistributionProvinceDetailDfDao;
    @Autowired
    AdmPrmVacViewspotBenchTrafficSumDfDao admPrmVacViewspotBenchTrafficSumDfDao;
    @Autowired
    CdmLogTtdViewspotBenchActivityIndexDfDao cdmLogTtdViewspotBenchActivityIndexDfDao;
    @Autowired
    CommonService commonService;
    @Autowired
    private RemoteConfig config;
    @Autowired
    TtdProductBasicServiceProxy ttdProductBasicServiceProxy;
    @Autowired
    GargleTranslateServiceProxy gargleTranslateServiceProxy;


    @Override
    public GetSalesMetricCardResponseType getSalesMetricCard(GetSalesMetricCardRequestType requestType, Boolean needFormatPercentage) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
        Integer businessType = commonFilter.getBusinessType();
        //供应商id列表
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        String queryD = commonService.getQueryD();

        List<String> metricEnglishNameList = SalesMetricEnumType.getMetricCardEnumEnglishNameList();
        //查表计算各指标值
        Map<String, Object> metricResult = cdmOrdTtdVstArchiveDfDao.querySalesMetricCard(sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList, queryD);

        //月环比
        String popEndDate = "";
        String popStartDate = "";
        try {
            popEndDate = DateUtil.getDayOfInterval(startDate,-1);
            int interval = DateUtil.getDateGap(startDate, endDate);
            popStartDate = DateUtil.getDayOfInterval(popEndDate, -1*interval);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Map<String, Object> metricResultPop = cdmOrdTtdVstArchiveDfDao.querySalesMetricCardPop(sightId, dateType, popStartDate, popEndDate, needSubSight, businessType, vendorIdList, queryD);


        for (String metricEnglishName : metricEnglishNameList) {
            if(metricResultPop.get(metricEnglishName) != null && !((Double)metricResultPop.get(metricEnglishName)).equals(0.0) && metricResult.get(metricEnglishName) != null && !metricResult.get(metricEnglishName).equals(0.0)) {
                metricResult.put(metricEnglishName + "_pop", ((Double) metricResult.get(metricEnglishName) - (Double) metricResultPop.get(metricEnglishName)) / (Double) metricResultPop.get(metricEnglishName));
            }
        }

        //去年同比
        Map<String, Object> metricResultYoy = cdmOrdTtdVstArchiveDfDao.querySalesMetricCardYoy(sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList, queryD);
        for (String metricEnglishName : metricEnglishNameList) {
            if(metricResultYoy.get(metricEnglishName) != null && !((Double)metricResultYoy.get(metricEnglishName)).equals(0.0) && metricResult.get(metricEnglishName) != null && !metricResult.get(metricEnglishName).equals(0.0)) {
                metricResult.put(metricEnglishName + "_yoy", ((Double) metricResult.get(metricEnglishName) - (Double) metricResultYoy.get(metricEnglishName)) / (Double) metricResultYoy.get(metricEnglishName));
            }
        }


        List<SightArchivesSalesMetric> salesMetricList = new ArrayList<>();
        for (String metricEnglishName : metricEnglishNameList) {
            SightArchivesSalesMetric salesMetric = new SightArchivesSalesMetric();
            salesMetric.setMetricName(metricEnglishName);
            salesMetric.setMetricValue((Double) metricResult.get(metricEnglishName));
            Double popValue = (Double) metricResult.getOrDefault(metricEnglishName + "_pop", 0.0d);
            Double yoyValue = (Double)  metricResult.getOrDefault(metricEnglishName + "_yoy", 0.0d);
            salesMetric.setPopValue(needFormatPercentage ? Double.valueOf(NumberFormatUtil.formatPercentage(popValue)) : popValue);
            salesMetric.setYoyValue(needFormatPercentage ? Double.valueOf(NumberFormatUtil.formatPercentage(yoyValue)) : yoyValue);
            salesMetricList.add(salesMetric);
        }

        GetSalesMetricCardResponseType responseType = new GetSalesMetricCardResponseType();
        responseType.setMetricList(salesMetricList);
        return responseType;
    }

    @Override
    public GetSalesMetricTrendLineResponseType getSalesMetricTrendLine(GetSalesMetricTrendLineRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
        Integer businessType = commonFilter.getBusinessType();
        //供应商id列表
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        //metric
        String metricName = requestType.getMetricName();

        String queryD = commonService.getQueryD();

        List<SightArchivesSalesMetricTrendLineItem> sightArchivesSalesMetricTrendLineItem=cdmOrdTtdVstArchiveDfDao.querySalesMetricTrendLine(queryD, sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList,metricName);
        GetSalesMetricTrendLineResponseType salesMetricTrendLineResponseType = new GetSalesMetricTrendLineResponseType();
        salesMetricTrendLineResponseType.setDateList(sightArchivesSalesMetricTrendLineItem);

        return  salesMetricTrendLineResponseType;
    }

    @Override
    public GetSalesMetricPieChartResponseType getSalesMetricPieChart(GetSalesMetricPieChartRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
        Integer businessType = commonFilter.getBusinessType();
        //供应商id列表
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        //指标名称
        String metricName = requestType.getMetricName();

        List<SalesPieChartEnumType> pieChartEnumList = new ArrayList<>();
        if(businessType == 3){
            pieChartEnumList = SalesPieChartEnumType.getNotTicketBuPieChartEnumList();
        } else {
            pieChartEnumList = SalesPieChartEnumType.getTicketBuPieChartEnumList();
        }

        String queryD = commonService.getQueryD();

        GetSalesMetricPieChartResponseType responseType = new GetSalesMetricPieChartResponseType();

        List<SightArchivesSalesMetricPieChart> pieChartList = new ArrayList<>();

        for(SalesPieChartEnumType salesPieChartEnumType : pieChartEnumList){
            SightArchivesSalesMetricPieChart pieChart = new SightArchivesSalesMetricPieChart();
            pieChart.setDillDownDim(salesPieChartEnumType.getEnglishName());
            //查下钻饼图内数据
//            String drillDownColumn = salesPieChartEnumType.getDrillDownColumn();
            List<SalesPieChartSegment> pieChartSegmentList = cdmOrdTtdVstArchiveDfDao.querySalesPieChart(queryD, sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList, salesPieChartEnumType,metricName);

            List<SalesPieChartSegment> yoyPieChartSegmentList = cdmOrdTtdVstArchiveDfDao.querySalesPieChartYoy(queryD, sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList, salesPieChartEnumType,metricName);
            List<SalesPieChartSegment> finalPieChartSegmentList = setYoyPieChartSegment(pieChartSegmentList, yoyPieChartSegmentList,salesPieChartEnumType,metricName);
            pieChart.setPieChartSegmentList(finalPieChartSegmentList);
            pieChartList.add(pieChart);
        }

        responseType.setPieChartList(pieChartList);
        if ("T".equals(config.getConfigValue("languageSwitch"))) {
            setMultiLanguage(responseType, queryD);
        }
        return responseType;
    }

    private void setMultiLanguage(GetSalesMetricPieChartResponseType responseType,String queryD) {
        List<SightArchivesSalesMetricPieChart> pieChartList = responseType.getPieChartList();
        if (CollectionUtils.isEmpty(pieChartList)) {
            return;
        }
        for (SightArchivesSalesMetricPieChart sightArchivesSalesMetricPieChart : pieChartList) {
            List<SalesPieChartSegment> pieChartSegmentList = sightArchivesSalesMetricPieChart.getPieChartSegmentList();
            if (CollectionUtils.isNotEmpty(pieChartSegmentList)) {
                for (SalesPieChartSegment salesPieChartSegment : pieChartSegmentList) {
                    if (StringUtils.isNotBlank(salesPieChartSegment.getName())) {
                        salesPieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage(salesPieChartSegment.getName(), UserUtil.getVbkLocaleForScenic()));
                    }
                    SaleUnitInfo saleUnitInfo = cdmOrdTtdVstArchiveDfDao.queryFirstSaleUnitId(queryD,salesPieChartSegment.getName());
                    if (saleUnitInfo.getSecondSaleUnitId() == null || saleUnitInfo.getSecondSaleUnitId() == 0) {
                        continue;
                    }
                    String name = ttdProductBasicServiceProxy.getPropertyValueName(saleUnitInfo);
                    if (name != null) {
                        salesPieChartSegment.setName(name);
                    }
                }
            }
        }
    }

    private List<SalesPieChartSegment> setYoyPieChartSegment(List<SalesPieChartSegment> pieChartSegmentList , List<SalesPieChartSegment> yoyPieChartSegmentList, SalesPieChartEnumType salesPieChartEnumType,String metricName){

        pieChartSegmentList.sort((o1, o2) -> {

            if (Objects.isNull(o2.getMetricValue()) || Objects.isNull(o1.getMetricValue())) {
                return -1;
            }

            return o2.getMetricValue().compareTo(o1.getMetricValue());
        });

        List<SalesPieChartSegment> newPieChartSegmentList = new ArrayList<>();
        List<SalesPieChartSegment> newYoyPieChartSegmentList = new ArrayList<>();
        if(SalesPieChartEnumType.TICKET_TYPE_DISTRIBUTION.equals(salesPieChartEnumType) && !metricName.equals(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()) && !metricName.equals(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName())){

            Set<String> set = new HashSet<>();
            double sum = 0.0;
            for(SalesPieChartSegment salesPieChartSegment : pieChartSegmentList) {
                if (Double.compare(sum, 0.98) < 0) {
                    sum += salesPieChartSegment.getMetricValue();
                    newPieChartSegmentList.add(salesPieChartSegment);
                } else {
                    set.add(salesPieChartSegment.getName());
                }
            }
            if(set.size() > 0){
                SalesPieChartSegment tempSalesPieChartSegment = new SalesPieChartSegment();
                tempSalesPieChartSegment.setName("其它");  //NOSONAR
                tempSalesPieChartSegment.setMetricValue(1-sum);
                newPieChartSegmentList.add(tempSalesPieChartSegment);
            }

            double yoySum = 0.0;
            for(SalesPieChartSegment yoySalesPieChartSegment : yoyPieChartSegmentList){
                if(set.contains(yoySalesPieChartSegment.getName())){
                    yoySum += yoySalesPieChartSegment.getYoyValue();
                }else {
                    newYoyPieChartSegmentList.add(yoySalesPieChartSegment);
                }
            }
            if(yoySum>0){
                SalesPieChartSegment salesPieChartSegment = new SalesPieChartSegment();
                salesPieChartSegment.setName("其它"); //NOSONAR
                salesPieChartSegment.setYoyValue(yoySum);
                newYoyPieChartSegmentList.add(salesPieChartSegment);
            }

        }else {
            newPieChartSegmentList = pieChartSegmentList;
            newYoyPieChartSegmentList = yoyPieChartSegmentList;
        }

        if(!metricName.equals(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName())){
            for(SalesPieChartSegment salesPieChartSegment : newPieChartSegmentList){
                for(SalesPieChartSegment yoySalesPieChartSegment : newYoyPieChartSegmentList){
                    if(salesPieChartSegment.getName().equals(yoySalesPieChartSegment.getName()) && salesPieChartSegment.getMetricValue() != null && yoySalesPieChartSegment.getYoyValue()!=null && yoySalesPieChartSegment.getYoyValue() != 0){
                        salesPieChartSegment.setYoyValue((salesPieChartSegment.getMetricValue() - yoySalesPieChartSegment.getYoyValue())/yoySalesPieChartSegment.getYoyValue());
                    }
                }
            }
        }

        return newPieChartSegmentList;
    }

    @Override
    public GetSalesMetricRankTableResponseType getSalesMetricRankTable(GetSalesMetricRankTableRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
        Integer businessType = commonFilter.getBusinessType();
        //供应商id列表
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        //指标名称
        String metricName = requestType.getMetricName();

        String queryD = commonService.getQueryD();

        //下钻表格的维度: 1-票种、2-供应商、3-分销商
        Integer drillDownDim = requestType.getDrillDownDim();
        Integer pageNo = requestType.getPageNo();
        Integer pageSize = requestType.getPageSize();

        List<SalesMetricTableRow> tableRowList = cdmOrdTtdVstArchiveDfDao.querySalesMetricRankTable(queryD, sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList,metricName,drillDownDim);

        tableRowList.removeIf(tableRow -> tableRow.getName().contains("unkwn"));

        //加上排名
        for(int i=0;i<tableRowList.size();i++){
            SalesMetricTableRow salesMetricTableRow = tableRowList.get(i);
            salesMetricTableRow.setRank(i+1);
        }

        if (pageNo == null || pageSize == null || pageNo <= 0 || pageSize <= 0) {
            pageNo = 1;
            pageSize = 10;
        }
        //分页
        int totalNum = tableRowList.size();
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, tableRowList.size());
        if (startIndex >= endIndex) {
            tableRowList = new ArrayList<>();
        }else {
            tableRowList = tableRowList.subList(startIndex, endIndex);
        }

        //一级票种翻译
        if(drillDownDim == 1
                && "T".equals(config.getConfigValue("languageSwitch"))
                && "en-US".equals(UserUtil.getVbkLocale()) ){
            List<Long> ticketIdList = new ArrayList<>();
            for (SalesMetricTableRow salesMetricTableRow : tableRowList) {
                ticketIdList.add(salesMetricTableRow.getId());
            }
            if(CollectionUtils.isNotEmpty(ticketIdList)){
                Map<Long,String> translateResultMap = ttdProductBasicServiceProxy.getFirstLevelSaleUnitInfo(ticketIdList);
                for(SalesMetricTableRow salesMetricTableRow : tableRowList){
                    Long ticketId = salesMetricTableRow.getId();
                    if (translateResultMap.containsKey(ticketId)) {
                        salesMetricTableRow.setName(translateResultMap.get(ticketId));
                    }
                }
            }
        }

        GetSalesMetricRankTableResponseType responseType = new GetSalesMetricRankTableResponseType();
        responseType.setTableRowList(tableRowList);
        responseType.setTotalNum(totalNum); //done
        return responseType;
    }

    @Override
    public GetCooperativeProjectOutputResponseType getCooperativeProjectOutput(GetCooperativeProjectOutputRequestType requestType) {
        GetCooperativeProjectOutputResponseType outputResponseType = new GetCooperativeProjectOutputResponseType();

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
        Integer businessType = commonFilter.getBusinessType();
        //供应商id列表
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        String queryD = commonService.getQueryD();

        List<CooperativeProject> result = cdmOrdTtdVstArchiveDfDao.queryCooperativeProjectOutput(queryD, sightId, dateType, startDate, endDate, needSubSight, businessType, vendorIdList);
        outputResponseType.setCooperativeProjectList(result);

        return outputResponseType;
    }

    @Override
    public GetMarketingCampaignResponseType getMarketingCampaign(GetMarketingCampaignRequestType requestType) {
        //数仓侧设计文档:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
        //数据源表: cdm_log_ttd_viewspot_bench_activity_index_df
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();

        String queryD = commonService.getQueryD();

        List<MarketingCampaign> marketingCampaignList = cdmLogTtdViewspotBenchActivityIndexDfDao.queryMarketingCampaignList(queryD, sightId, needSubSight, startDate, endDate);

        //翻译营销活动名称和子活动名称
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale()) ) {
            List<String> textList = new ArrayList<>();
            for (MarketingCampaign marketingCampaign : marketingCampaignList) {
                if(StringUtils.isNotBlank(marketingCampaign.getMarketingCampaignName())) {
                    textList.add(marketingCampaign.getMarketingCampaignName());
                }
                if (StringUtils.isNotBlank(marketingCampaign.getName())) {
                    textList.add(marketingCampaign.getName());
                }
            }

            Map<String, String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(textList);
            for (MarketingCampaign marketingCampaign : marketingCampaignList) {
                String marketingCampaignName = marketingCampaign.getMarketingCampaignName();
                String name = marketingCampaign.getName();
                if (StringUtils.isNotBlank(marketingCampaignName) && translateResultMap.containsKey(marketingCampaignName)) {
                    marketingCampaign.setMarketingCampaignName(translateResultMap.get(marketingCampaignName));
                }
                if (StringUtils.isNotBlank(name) && translateResultMap.containsKey(name)) {
                    marketingCampaign.setName(translateResultMap.get(name));
                }
            }
        }

        GetMarketingCampaignResponseType responseType = new GetMarketingCampaignResponseType();
        responseType.setMarketingCampaignList(marketingCampaignList);
        return responseType;
    }

    @Override
    public GetAdvertisingPlacementResponseType getAdvertisingPlacement(GetAdvertisingPlacementRequestType requestType) {
        //数仓侧设计文档:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3669341013
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
//        //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
//        Integer dateType = commonFilter.getDateType();
//        //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
//        Integer businessType = commonFilter.getBusinessType();
//        //供应商id列表
//        List<Long> vendorIdList = commonFilter.getVendorIdList();

        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        Integer pageNo = requestType.getPageNo();
        Integer pageSize = requestType.getPageSize();

        String queryD = commonService.getQueryD();

        GetAdvertisingPlacementResponseType responseType = new GetAdvertisingPlacementResponseType();
        //数据源表:adm_prm_vac_viewspot_bench_traffic_sum_df
        List<DeliveryPage> deliveryPageList = admPrmVacViewspotBenchTrafficSumDfDao.queryDeliveryPageList(queryD, sightId, needSubSight, startDate, endDate, pageNo, pageSize);

        //加上排名
        int rank = (pageNo-1)*10;
        for(int i=0;i<deliveryPageList.size();i++){
            DeliveryPage deliveryPage = deliveryPageList.get(i);
            deliveryPage.setRank(rank+i+1);
        }



        responseType.setDeliveryPageList(deliveryPageList);
        Integer totalNum = admPrmVacViewspotBenchTrafficSumDfDao.queryDeliveryPageListTotalNum(queryD, sightId, needSubSight, startDate, endDate);
        responseType.setTotalNum(totalNum);


        //数据源表:adm_prm_vac_viewspot_distribution_province_detail_df
        List<DeliveryProvince> deliveryProvinceList = new ArrayList<>();
        List<String> provinceList = admPrmVacViewspotDistributionProvinceDetailDfDao.queryDeliveryProvinceList(queryD,sightId, needSubSight);
        for(String provinceName : provinceList){
            DeliveryProvince deliveryProvince = new DeliveryProvince();
            deliveryProvince.setName(provinceName);
            deliveryProvince.setTranslateName(provinceName);
            deliveryProvince.setCount(1);
            deliveryProvinceList.add(deliveryProvince);
        }

        //多语言
        if ("T".equals(config.getConfigValue("languageSwitch"))
                && "en-US".equals(UserUtil.getVbkLocale())) {

            for(DeliveryPage deliveryPage : deliveryPageList){
                //todo 广告投放位置名称(已完成)
                deliveryPage.setDeliveryPageName(ScenicLanguageHelper.getMultiLanguage(deliveryPage.getDeliveryPageName(), UserUtil.getVbkLocaleForScenic()));
            }

            Map<String,String> provinceMap = gargleTranslateServiceProxy.googleBatchTranslate(provinceList);

            for (DeliveryProvince province : deliveryProvinceList) {
                //todo  广告投放省份名称
                if (provinceMap.containsKey(province.getTranslateName())) {
                    province.setTranslateName(provinceMap.get(province.getTranslateName()));
                } else {
                    province.setTranslateName(province.getName());
                }
            }
        }

        responseType.setDeliveryProvinceList(deliveryProvinceList);

        return responseType;
    }
}
