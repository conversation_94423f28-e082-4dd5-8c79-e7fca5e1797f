package com.ctrip.tour.business.dashboard.soa;

import com.alibaba.fastjson.TypeReference;
import com.ctrip.basebiz.accounts.mobile.request.filter.WithAccountsMobileRequestFilter;
import com.ctrip.basebiz.accounts.mobile.request.filter.bean.AuthenticationModeEnum;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.service.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.CustTourDateTypeEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.KeyProjectDashboardBiz;
import com.ctrip.tour.business.dashboard.grpBusiness.service.GrpBusinessService;
import com.ctrip.tour.business.dashboard.tktBusiness.annotation.NotLoginRequired;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowHelper;
import com.ctrip.tour.business.dashboard.tktExternal.SrvQualityDimAssembleService;
import com.ctrip.tour.business.dashboard.tktExternal.UserDimAssembleService;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ObjectUtil;
import com.ctrip.tour.business.dashboard.utils.RedisUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.train.tieyouflight.soa.exception.annotation.EnableGlobalExceptionHandler;
import com.ctrip.train.tieyouflight.soa.validation.annotation.EnableRequestValidator;
import com.ctrip.train.tieyouflight.soa.validation.annotation.ValidateRequest;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import credis.java.client.CacheProvider;
import org.springframework.web.bind.annotation.CrossOrigin;
import tour.auth.soa.annotation.AuthTourUser;
import tour.auth.soa.annotation.AuthTourUserList;
import tour.auth.soa.model.AuthType;
import tour.auth.soa.model.ServiceInvokeChannel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务工作台服务接口
 *
 * <AUTHOR>
 * @date 2022/7/12
 */

@Component
@EnableGlobalExceptionHandler
@EnableRequestValidator
public class BIBusinessDashboardServiceImpl implements BIBusinessDashboardService {


    @Autowired
    private FilterBoxBiz filterBoxBiz;

    @Autowired
    private UserPermissionBiz userPermissionBiz;

    @Autowired
    private MetricDataBiz metricDataBiz;

    @Autowired
    private OverseaMetricDataBiz overseaMetricDataBiz;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    @Autowired
    private UserInfoBiz userInfoBiz;

    @Autowired
    private TaskFlowBiz taskFlowBiz;

    @Autowired
    private TaskFlowMetricCardBiz taskFlowMetricCardBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private TicketPKBiz ticketPKBiz;

    @Autowired
    private SelfServiceQualityPushBiz qualityPushBiz;

    @Autowired
    private GrpBusinessService grpBusinessService;

    @Autowired
    private KeyProjectDashboardBiz keyProjectDashboardBiz;

    @Autowired
    private CommonService commonService;
    @Autowired
    private SalesService salesService;
    @Autowired
    private FlowService flowService;
    @Autowired
    private UserProfileService userProfileService;
    @Autowired
    private QualityService qualityService;
    @Autowired
    private CompetitiveService competitiveService;
    @Autowired
    private MarketService marketService;
    @Autowired
    private UserDimAssembleService userDimAssembleService;
    @Autowired
    private SrvQualityDimAssembleService srvQualityDimAssembleService;

    @Autowired
    private CustEmpOrgInfoService custEmpOrgInfoService;

    @Autowired
    private HrOrgEmpInfoService hrOrgEmpInfoService;


    @Override
    @NotLoginRequired
    public CheckHealthResponseType checkHealth(CheckHealthRequestType checkHealthRequestType) throws Exception {
        return new CheckHealthResponseType();
    }

    @Override
    public GetUpdateTimeResponseType getUpdateTime(GetUpdateTimeRequestType getUpdateTimeRequestType) throws Exception {
        return dataUpdateBiz.getUpdateTime(getUpdateTimeRequestType);
    }

    @Override
    @ValidateRequest
    public GetOverseaMetricCardDataResponseType getOverseaMetricCardData(GetOverseaMetricCardDataRequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaMetricCardData(request);
    }

    @Override
    public GetOverseaMetricCardDataV2ResponseType getOverseaMetricCardDataV2(GetOverseaMetricCardDataV2RequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaMetricCardDataV2(request);
    }

    @Override
    @ValidateRequest
    public GetOverseaTrendLineDataResponseType getOverseaTrendLineData(GetOverseaTrendLineDataRequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaTrendLineData(request);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getOverseaTrendLineDataV2(GetOverseaTrendLineDataV2RequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaTrendLineDataV2(request);
    }

    @Override
    @ValidateRequest
    public GetOverseaDrillDownBaseInfoResponseType getOverseaDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaDrillDownBaseInfo(request);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaDrillDownBaseInfoV2(GetOverseaDrillDownBaseInfoV2RequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaDrillDownBaseInfoV2(request);
    }

    @Override
    @ValidateRequest
    public GetOverseaTableDataResponseType getOverseaTableData(GetOverseaTableDataRequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaTableData(request);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getOverseaTableDataV2(GetOverseaTableDataV2RequestType request) throws Exception {
        return overseaMetricDataBiz.getOverseaTableDataV2(request);
    }

    @AuthTourUserList(userList = {
            @AuthTourUser(authType = AuthType.EID, needExtendInfo = true, channels = ServiceInvokeChannel.SOA_OFFLINE),
            @AuthTourUser(authType = AuthType.UID, channels = ServiceInvokeChannel.SOA_H5)
    })
    @Override
    public CheckUserPermissionResponseType checkUserPermission(CheckUserPermissionRequestType checkUserPermissionRequestType) throws Exception {
        return userPermissionBiz.checkUserPermission(checkUserPermissionRequestType);
    }

    @Override
    public CheckAdminPermissionResponseType checkAdminPermission(CheckAdminPermissionRequestType checkAdminPermissionRequestType) throws Exception {
        return userPermissionBiz.checkAdminPermission(checkAdminPermissionRequestType);
    }

    @Override
    @ValidateRequest
    public GetTableDataResponseType getTableData(GetTableDataRequestType getTableDataRequestType) throws Exception {
        return metricDataBiz.getTableData(getTableDataRequestType);
    }

    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(GetFirstPageDomesticMetricCardDrillDataRequestType getFirstPageDomesticMetricCardDrillDataRequestType) throws Exception {
        return metricDataBiz.getFirstPageDomesticMetricCardDrillData(getFirstPageDomesticMetricCardDrillDataRequestType);
    }

    @Override
    public GetDomesticTableDataResponseType getDomesticTableData(GetDomesticTableDataRequestType getDomesticTableDataRequestType) throws Exception {
        return metricDataBiz.getDomesticTableData(getDomesticTableDataRequestType);
    }

    @Override
    public GraphResponseType getGraph(GraphRequestType graphRequestType) throws Exception {
        return null;
    }

    @Override
    public DownloadTableDataResponseType downloadTableData(DownloadTableDataRequestType downloadTableDataRequestType) throws Exception {
        return new DownloadTableDataResponseType();
    }

    @Override
    public GetUserInfoResponseType getUserInfo(GetUserInfoRequestType getUserInfoRequestType) throws Exception {
        return userInfoBiz.getUserInfo(getUserInfoRequestType);
    }

    @Override
    public GetTaskLevelDimResponseType getTaskLevelDim(GetTaskLevelDimRequestType getTaskLevelDimRequestType) throws Exception {
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, "");
        return taskFlowBiz.getTaskLevelDim(getTaskLevelDimRequestType, empCode);
    }


    @Override
    public GetTaskFlowMetricCardDataResponseType getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType getTaskFlowMetricCardDataRequestType) throws Exception {
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, getTaskFlowMetricCardDataRequestType.getStatisticalScope());
        return taskFlowMetricCardBiz.getTaskFlowMetricCardData(getTaskFlowMetricCardDataRequestType, empCode, true).get();
    }


    @Override
    public DownloadTaskFlowDataResponseType downloadTaskFlowData(GetTaskFlowMetricCardDataRequestType getTaskFlowMetricCardDataRequestType) throws Exception {
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, getTaskFlowMetricCardDataRequestType.getStatisticalScope());
        return taskFlowMetricCardBiz.downloadTaskFlowData(getTaskFlowMetricCardDataRequestType, empCode);
    }

    @Override
    public GetTaskFlowTableDataResponseType getTaskFlowTableData(GetTaskFlowTableDataRequestType getTaskFlowTableDataRequestType) throws Exception {
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, getTaskFlowTableDataRequestType.getStatisticalScope());
        return taskFlowBiz.getTaskFlowTableData(getTaskFlowTableDataRequestType, empCode);
    }


    @Override
    public CheckTaskFlowPermissionResponseType checkTaskFlowPermission(CheckTaskFlowPermissionRequestType checkTaskFlowPermissionRequestType) throws Exception {
        String mappingEmpCode = UserUtil.getTaskFlowMappingEmpCode(remoteConfig);
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, "");
        CheckTaskFlowPermissionResponseType responseType = userPermissionBiz.checkTaskFlowPermission(checkTaskFlowPermissionRequestType, empCode);

        //老季对于"统计范围"字段的特殊逻辑
        String admin = remoteConfig.getExternalConfig("admin");
        if (admin.equals(mappingEmpCode)) {
            responseType.setStatisticalScopeList(new ArrayList<>(TaskFlowHelper.getTaskStatisticalScopeMap().values()));
        }

        return responseType;
    }



    @Override
    public GetTaskFlowTrendlineDataResponseType getTaskFlowTrendlineData(GetTaskFlowTrendlineDataRequestType getTaskFlowTrendlineDataRequestType) throws Exception {
        String empCode = UserUtil.getTaskFlowMappingEmpCodeByTaskStatisticalScope(remoteConfig, getTaskFlowTrendlineDataRequestType.getStatisticalScope());
        return taskFlowBiz.getTaskFlowTrendlineData(getTaskFlowTrendlineDataRequestType, empCode);
    }

    @Override
    public GetTaskLevelScoreMappingResponseType getTaskLevelScoreMapping(GetTaskLevelScoreMappingRequestType getTaskLevelScoreMappingRequestType) throws Exception {
        return taskFlowBiz.getTaskLevelScoreMapping(getTaskLevelScoreMappingRequestType);
    }

    @Override
    @ValidateRequest
    public GetTrendLineDataResponseType getTrendLineData(GetTrendLineDataRequestType getTrendLineDataRequestType) throws Exception {
        return metricDataBiz.getTrendLineData(getTrendLineDataRequestType);
    }

    @Override
    @ValidateRequest
    public GetDrillDownBaseInfoResponseType getDrillDownBaseInfo(GetDrillDownBaseInfoRequestType getDrillDownBaseInfoRequestType) throws Exception {
        return metricDataBiz.getDrillDownBaseInfo(getDrillDownBaseInfoRequestType);
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getDomesticDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType getDomesticDrillDownBaseInfoRequestType) throws Exception {
        return metricDataBiz.getDomesticDrillDownBaseInfo(getDomesticDrillDownBaseInfoRequestType);
    }

    @Override
    public GetEmployeeByFilterResponseType getEmployeeByFilter(GetEmployeeByFilterRequestType getEmployeeByFilterRequestType) throws Exception {
        return filterBoxBiz.getEmployeeByFilter(getEmployeeByFilterRequestType);
    }

    @Override
    @ValidateRequest
    public GetMetricCardDataResponseType getMetricCardData(GetMetricCardDataRequestType getMetricCardDataRequestType) throws Exception {
        return metricDataBiz.getMetricCardData(getMetricCardDataRequestType);
    }


    @Override
    public GetOrganizationByFilterResponseType getOrganizationByFilter(GetOrganizationByFilterRequestType getOrganizationByFilterRequestType) throws Exception {
        return filterBoxBiz.getOrganizationByFilter(getOrganizationByFilterRequestType);
    }

    // pk

    @Override
    public GetTicketPKEnumDataResponseType getTicketPKEnumData(GetTicketPKEnumDataRequestType getTicketPKEnumDataRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKEnumData(
                        ObjectUtil.deepJsonCopy(getTicketPKEnumDataRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataRequestType.class)
                ), GetTicketPKEnumDataResponseType.class
        );
    }

    @Override
    public GetTicketPKSaleunitRankingResponseType getTicketPKSaleunitRanking(GetTicketPKSaleunitRankingRequestType getTicketPKSaleunitRankingRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKSaleunitRanking(
                        ObjectUtil.deepJsonCopy(getTicketPKSaleunitRankingRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingRequestType.class)
                ), GetTicketPKSaleunitRankingResponseType.class
        );
    }

    @Override
    @NotLoginRequired
    public GetUserDimDetailInfoResponseType getUserDimDetailInfo(GetUserDimDetailInfoRequestType getUserDimDetailInfoRequestType) throws Exception {
        return userDimAssembleService.getUserDimDetailInfo(getUserDimDetailInfoRequestType);
    }

    @Override
    @NotLoginRequired
    public GetServiceQualityDimDetailInfoResponseType getServiceQualityDimDetailInfo(GetServiceQualityDimDetailInfoRequestType getServiceQualityDimDetailInfoRequestType) throws Exception {
        return srvQualityDimAssembleService.getSrvQualityDimInfo(getServiceQualityDimDetailInfoRequestType);
    }

    @Override
    public GetGrpOrgTabListResponseType getGrpOrgTabList(GetGrpOrgTabListRequestType getGrpOrgTabListRequestType) throws Exception {
        GetGrpOrgTabListResponseType grpOrgTabListV2 = hrOrgEmpInfoService.getGrpOrgTabListV2();
        List<Integer> collect = grpOrgTabListV2.getTabList().stream().map(v -> {
            if (v == 100) {
                return 1;
            } else if (v == 210) {
                return 2;
            } else if (v == 220) {
                return 2;
            } else if (v == 230) {
                return 3;
            }
            return -1;
        }).filter(v -> v != -1).distinct().collect(Collectors.toList());
        // grpOrgTabList
        GetGrpOrgTabListResponseType grpOrgTabList = new GetGrpOrgTabListResponseType();
        grpOrgTabList.setTabList(collect);
        if (!grpOrgTabList.getTabList().isEmpty()) {
            grpOrgTabList.setDefaultTabList(grpOrgTabList.getTabList().get(0));
        }
        return grpOrgTabList;
//        return hrOrgEmpInfoService.getGrpOrgTabList();
    }

    @Override
    public GetGrpOrgTabListResponseType getGrpOrgTabListV2(GetGrpOrgTabListRequestType getGrpOrgTabListRequestType) throws Exception {
        return hrOrgEmpInfoService.getGrpOrgTabListV2();
    }

    @Override
    public GetCustTourDateTypeResponseType getCustTourDateType(GetCustTourDateTypeRequestType getCustTourDateTypeRequestType) throws Exception {

        GetCustTourDateTypeResponseType getCustTourDateTypeResponseType = new GetCustTourDateTypeResponseType();
        getCustTourDateTypeResponseType.setDateTypeList(Arrays.stream(CustTourDateTypeEnum.values()).map(Enum::name).collect(Collectors.toList()));
        return getCustTourDateTypeResponseType;
    }

    @Override
    public GetCustTourRegionListResponseType getCustTourRegionList(GetCustTourRegionListRequestType getCustTourRegionListRequestType) throws Exception {
        GetCustTourRegionListResponseType responseType = new GetCustTourRegionListResponseType();
        List<CustTourRegionInfo> regionList = custEmpOrgInfoService.getCustTourRegionList(null);
        responseType.setRegionList(regionList);
        return responseType;
    }

    @Override
    public GetTicketPKResourceRankingResponseType getTicketPKResourceRanking(GetTicketPKResourceRankingRequestType getTicketPKResourceRankingRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKResourceRanking(
                        ObjectUtil.deepJsonCopy(getTicketPKResourceRankingRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingRequestType.class)
                ), GetTicketPKResourceRankingResponseType.class
        );
    }

    @Override
    public GetTicketPKScheduleCalendarResponseType getTicketPKScheduleCalendar(GetTicketPKScheduleCalendarRequestType getTicketPKScheduleCalendarRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKScheduleCalendar(
                        ObjectUtil.deepJsonCopy(getTicketPKScheduleCalendarRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarRequestType.class)
                ), GetTicketPKScheduleCalendarResponseType.class
        );
    }

    @Override
    public GetTicketPKQualificationResponseType getTicketPKQualification(GetTicketPKQualificationRequestType getTicketPKQualificationRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKQualification(
                        ObjectUtil.deepJsonCopy(getTicketPKQualificationRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKQualificationRequestType.class)
                ), GetTicketPKQualificationResponseType.class
        );
    }

    @Override
    public GetTicketPKTableResponseType getTicketPKTable(GetTicketPKTableRequestType getTicketPKTableRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKTable(
                        ObjectUtil.deepJsonCopy(getTicketPKTableRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKTableRequestType.class)
                ), GetTicketPKTableResponseType.class
        );
    }

    @Override
    @ValidateRequest
    public GetTicketPKBestStatusResponseType getTicketPKBestStatus(GetTicketPKBestStatusRequestType getTicketPKBestStatusRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKBestStatus(
                        ObjectUtil.deepJsonCopy(getTicketPKBestStatusRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusRequestType.class)
                ), GetTicketPKBestStatusResponseType.class
        );
    }

    @Override
    public GetTicketPKDefectOverallDataResponseType getTicketPKDefectOverallData(GetTicketPKDefectOverallDataRequestType getTicketPKDefectOverallDataRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKDefectOverallData(
                        ObjectUtil.deepJsonCopy(getTicketPKDefectOverallDataRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataRequestType.class)
                ), GetTicketPKDefectOverallDataResponseType.class
        );
    }

    @Override
    public GetTicketPKDefectTableDataResponseType getTicketPKDefectTableData(GetTicketPKDefectTableDataRequestType getTicketPKDefectTableDataRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKDefectTableData(
                        ObjectUtil.deepJsonCopy(getTicketPKDefectTableDataRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataRequestType.class)
                ), GetTicketPKDefectTableDataResponseType.class
        );
    }

    @Override
    public GetTicketPKDefectOrderIdResponseType getTicketPKDefectOrderId(GetTicketPKDefectOrderIdRequestType getTicketPKDefectOrderIdRequestType) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getTicketPKDefectOrderId(
                        ObjectUtil.deepJsonCopy(getTicketPKDefectOrderIdRequestType, com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdRequestType.class)
                ), GetTicketPKDefectOrderIdResponseType.class
        );
    }

    @Override
    public GetPreviewInfoResponseType getPreviewInfo(GetPreviewInfoRequestType request) throws Exception {
        return ticketPKBiz.getPreviewInfo(request);
    }

    @Override
    public GetMixProductPreviewStatusResponseType getMixProductPreviewStatus(GetMixProductPreviewStatusRequestType request) throws Exception {
        return ObjectUtil.deepJsonCopy(
                ticketPKBiz.getMixProductPreviewStatus(
                        ObjectUtil.deepJsonCopy(request, com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusRequestType.class)
                ), GetMixProductPreviewStatusResponseType.class
        );
    }


    @Override
    @NotLoginRequired
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.BanH5Request)
    public PushSelfQualityOfServiceToBusinessResponseType pushSelfQualityOfServiceToBusiness(PushSelfQualityOfServiceToBusinessRequestType request) throws Exception {
        return qualityPushBiz.pushSelfQualityOfServiceToBusiness(request);
    }

    @Override
    public CheckEmpBusinessTypeResponseType checkEmpBusinessType(CheckEmpBusinessTypeRequestType requestType) throws Exception {
        return keyProjectDashboardBiz.checkEmpBusinessTypeNew(requestType);
    }

    @Override
    public GetKeyProjectDashboardConfigResponseType getKeyProjectDashboardConfig(GetKeyProjectDashboardConfigRequestType requestType) throws Exception {
        return keyProjectDashboardBiz.getKeyProjectDashboardConfig(requestType);
    }

    @Override
    public GetKeyProjectDashboardFilterEnumsResponseType getKeyProjectDashboardFilterEnums(GetKeyProjectDashboardFilterEnumsRequestType requestType) throws Exception {
        return keyProjectDashboardBiz.getKeyProjectDashboardFilterEnums(requestType);
    }


    @Override
    public QueryByDSLResponseType queryDataByDSL(QueryByDSLRequestType queryDataByDSLRequestType) throws Exception {
        return grpBusinessService.queryDataByDSL(queryDataByDSLRequestType.getDsl());
    }

    @Override
    public PerformanceCardResponseType queryPerformanceCard(PerformanceCardRequestType performanceCardRequestType) throws Exception {
        return grpBusinessService.queryPerformanceCard(performanceCardRequestType);
    }

    @Override
    public ChangeTrendResponseType queryChangeTrend(ChangeTrendRequestType changeTrendRequestType) throws Exception {
        return grpBusinessService.queryChangeTrend(changeTrendRequestType);
    }

    @Override
    public TopRankingResponseType queryTopRanking(TopRankingRequestType topRankingRequestType) throws Exception {
        return grpBusinessService.queryTopRanking(topRankingRequestType);
    }


    //---------------------------- 以下为跟团的接口 ----------------------------

    @Override
    public GetGrpUpdateTimeResponseType getGrpUpdateTime(GetGrpUpdateTimeRequestType requestType) throws Exception {
        return grpBusinessService.getGrpUpdateTime(requestType);
    }

    @Override
    public GetGrpDestProvListResponseType getGrpDestProvList(GetGrpDestProvListRequestType getGrpDestProvListRequestType) throws Exception {
        GetGrpDestProvListResponseType provListResponseType = new GetGrpDestProvListResponseType();
        CacheProvider provider = RedisUtil.getProvider(remoteConfig.getConfigValue("profileRedisName"));
        String destAreaStr = RedisUtil.get(provider, "tour_bi_grp_dest_area");
        provListResponseType.setProvNameList(MapperUtil.str2List(destAreaStr, String.class));
        return provListResponseType;

    }

    @Override
    public GetGrpOrganizationResponseType getGrpOrganization(GetGrpOrganizationRequestType requestType) throws Exception {
        return grpBusinessService.getGrpOrganization(requestType);
    }


    @Override
    public GetGrpOrganizationResponseType getGrpOrganizationV2(GetGrpOrganizationRequestType requestType) throws Exception {
        return grpBusinessService.getGrpOrganizationV2(requestType);
    }


    @Override
    public GetGrpEmployeeResponseType getGrpEmployee(GetGrpEmployeeRequestType requestType) throws Exception {
        return grpBusinessService.getGrpEmployeeV2(requestType);
    }

    @Override
    public GetGrpProductPatternResponseType getGrpProductPattern(GetGrpProductPatternRequestType requestType) throws Exception {
        return grpBusinessService.getGrpProductPattern(requestType);
    }

    @Override
    public GetGrpProductPatternResponseType getGrpProductPatternV2(GetGrpProductPatternRequestType requestType) throws Exception {
        return grpBusinessService.getGrpProductPatternV2(requestType);
    }

    @Override
    public GetGrpMetricCardResponseType getGrpMetricCard(GetGrpMetricDataRequestType requestType) throws Exception {
        return grpBusinessService.getMetricCard(requestType, null);
    }

    @Override
    public GetGrpTrendLineResponseType getGrpTrendLine(GetGrpMetricDataRequestType requestType) throws Exception {
        return grpBusinessService.getTrendLineInfo(requestType);
    }

    @Override
    public GetGrpDillDownDimResponseType getGrpDillDownDim(GetGrpDillDownDimRequestType requestType) throws Exception {
        return grpBusinessService.getGrpDillDownDim(requestType);
    }

    @Override
    public GetGrpDillDownDimResponseTypeV2 getGrpDillDownDimV2(GetGrpDillDownDimRequestTypeV2 requestType) throws Exception {
        return grpBusinessService.getGrpDillDownDimV2(requestType);
    }

    @Override
    public GetGrpDillDownDimEnumResponseType getGrpDillDownDimEnum(GetGrpDillDownDimEnumRequestType requestType) throws Exception {
        return grpBusinessService.getGrpDillDownDimEnum(requestType);
    }

    @Override
    public GetGrpDillDownDetailResponseType getGrpDillDownDetail(GetGrpMetricDataRequestType requestType) throws Exception {
        return grpBusinessService.getGrpDillDownDetail(requestType);
    }

    @Override
    public DownloadGrpDillDownDetailResponseType downloadGrpDillDownDetail(DownloadGrpDillDownDetailRequestType requestType) throws Exception {
        return grpBusinessService.downloadGrpDillDownDetail(requestType);
    }

    @Override
    public GetGrpFirstPageMetricCardResponseType getGrpFirstPageMetricCard(GetGrpFirstPageMetricCardRequestType requestType) throws Exception {
        return grpBusinessService.getGrpFirstPageMetricCard(requestType);
    }


    //---------------------------- 以上为跟团的接口 ----------------------------


    //---------------------------- 以下为门票工作台-景点档案接口 ----------------------------
    @AuthTourUserList(userList = {
            @AuthTourUser(authType = AuthType.EID, needExtendInfo = true, channels = ServiceInvokeChannel.SOA_OFFLINE)
    })
    @Override
    public CheckSightArchivesPermissionResponseType checkSightArchivesPermission(CheckSightArchivesPermissionRequestType requestType) throws Exception {
        return commonService.checkSightArchivesPermission(requestType);
    }

    @Override
    public GetSightArchivesUpdateTimeResponseType getSightArchivesUpdateTime(GetSightArchivesUpdateTimeRequestType requestType) throws Exception {
        return commonService.getSightArchivesUpdateTime(requestType);
    }

    @Override
    public SearchSightListResponseType searchSightList(SearchSightListRequestType requestType) throws Exception {
        return commonService.searchSightList(requestType);
    }

    @Override
    public GetSightInfoResponseType getSightInfo(GetSightInfoRequestType requestType) throws Exception {
        return commonService.getSightInfo(requestType);
    }

    @Override
    public UpdateAnnualIntakeResponseType updateAnnualIntake(UpdateAnnualIntakeRequestType requestType) throws Exception {
        return commonService.updateAnnualIntake(requestType);
    }

    @Override
    public SearchVendorListResponseType searchVendorList(SearchVendorListRequestType requestType) throws Exception {
        return commonService.searchVendorList(requestType);
    }

    @Override
    public GetSightArchivesReportSummaryResponseType getSightArchivesReportSummary(GetSightArchivesReportSummaryRequestType requestType) throws Exception {
        return commonService.getSightArchivesReportSummary(requestType);
    }

    @Override
    public GetSalesMetricCardResponseType getSalesMetricCard(GetSalesMetricCardRequestType requestType) throws Exception {
        return salesService.getSalesMetricCard(requestType, false);
    }

    @Override
    public GetSalesMetricTrendLineResponseType getSalesMetricTrendLine(GetSalesMetricTrendLineRequestType requestType) throws Exception {
        return salesService.getSalesMetricTrendLine(requestType);
    }

    @Override
    public GetSalesMetricPieChartResponseType getSalesMetricPieChart(GetSalesMetricPieChartRequestType requestType) throws Exception {
        return salesService.getSalesMetricPieChart(requestType);
    }

    @Override
    public GetSalesMetricRankTableResponseType getSalesMetricRankTable(GetSalesMetricRankTableRequestType requestType) throws Exception {
        return salesService.getSalesMetricRankTable(requestType);
    }

    @Override
    public GetCooperativeProjectOutputResponseType getCooperativeProjectOutput(GetCooperativeProjectOutputRequestType requestType) throws Exception {
        return salesService.getCooperativeProjectOutput(requestType);
    }

    @Override
    public GetMarketingCampaignResponseType getMarketingCampaign(GetMarketingCampaignRequestType requestType) throws Exception {
        return salesService.getMarketingCampaign(requestType);
    }

    @Override
    public GetAdvertisingPlacementResponseType getAdvertisingPlacement(GetAdvertisingPlacementRequestType requestType) throws Exception {
        return salesService.getAdvertisingPlacement(requestType);
    }

    @Override
    public GetFlowMetricResponseType getFlowMetric(GetFlowMetricRequestType requestType) throws Exception {
        return flowService.getFlowMetric(requestType);
    }

    @Override
    public GetFlowMetricTrendLineResponseType getFlowMetricTrendLine(GetFlowMetricTrendLineRequestType requestType) throws Exception {
        return flowService.getFlowMetricTrendLine(requestType);
    }

    @Override
    public GetUserProfileHistogramResponseType getUserProfileHistogram(GetUserProfileHistogramRequestType requestType) throws Exception {
        return userProfileService.getUserProfileHistogram(requestType);
    }

    @Override
    public GetUserProfilePieChartResponseType getUserProfilePieChart(GetUserProfilePieChartRequestType requestType) throws Exception {
        return userProfileService.getUserProfilePieChart(requestType);
    }

    @Override
    public GetUserResidenceDistributionResponseType getUserResidenceDistribution(GetUserResidenceDistributionRequestType requestType) throws Exception {
        return userProfileService.getUserResidenceDistribution(requestType);
    }

    @Override
    public GetUserSearchPreferenceResponseType getUserSearchPreference(GetUserSearchPreferenceRequestType requestType) throws Exception {
        return userProfileService.getUserSearchPreference(requestType);
    }

    @Override
    public GetFulfillmentQualityMetricResponseType getFulfillmentQualityMetric(GetFulfillmentQualityMetricRequestType requestType) throws Exception {
        return qualityService.getFulfillmentQualityMetric(requestType);
    }

    @Override
    public GetFulfillmentQualityTableResponseType getFulfillmentQualityTable(GetFulfillmentQualityTableRequestType requestType) throws Exception {
        return qualityService.getFulfillmentQualityTable(requestType);
    }

    @Override
    public GetCommentMetricResponseType getCommentMetric(GetCommentMetricRequestType requestType) throws Exception {
        return qualityService.getCommentMetric(requestType);
    }

    @Override
    public GetServiceMetricResponseType getServiceMetric(GetServiceMetricRequestType requestType) throws Exception {
        return qualityService.getServiceMetric(requestType);
    }

    @Override
    public GetComplaintMetricResponseType getComplaintMetric(GetComplaintMetricRequestType requestType) throws Exception {
        return qualityService.getComplaintMetric(requestType);
    }

    @Override
    public GetVendorQualityTableResponseType getVendorQualityTable(GetVendorQualityTableRequestType requestType) throws Exception {
        return qualityService.getVendorQualityTable(requestType);
    }

    @Override
    public GetSightComparisonResponseType getSightComparison(GetSightComparisonRequestType requestType) throws Exception {
        return competitiveService.getSightComparison(requestType);
    }

    @Override
    public GetSightCompetitiveResponseType getSightCompetitive(GetSightCompetitiveRequestType requestType) throws Exception {
        return competitiveService.getSightCompetitive(requestType);
    }

    @Override
    public GetUncoveredTicketTypeResponseType getUncoveredTicketType(GetUncoveredTicketTypeRequestType requestType) throws Exception {
        return competitiveService.getUncoveredTicketType(requestType);
    }

    @Override
    public GetCoreTicketTypeCompetitiveResponseType getCoreTicketTypeCompetitive(GetCoreTicketTypeCompetitiveRequestType requestType) throws Exception {
        return competitiveService.getCoreTicketTypeCompetitive(requestType);
    }

    @Override
    public GetCoreTicketTypeCompetitiveDetailResponseType getCoreTicketTypeCompetitiveDetail(GetCoreTicketTypeCompetitiveDetailRequestType requestType) throws Exception {
        return competitiveService.getCoreTicketTypeCompetitiveDetail(requestType);
    }

    @Override
    public GetLocationHeatForecastResponseType getLocationHeatForecast(GetLocationHeatForecastRequestType requestType) throws Exception {
        return marketService.getLocationHeatForecast(requestType);
    }

    @Override
    public GetPopularSightsResponseType getPopularSights(GetPopularSightsRequestType requestType) throws Exception {
        return marketService.getPopularSights(requestType);
    }

    @Override
    public GetAiModuleSummaryResponseType getAiModuleSummary(GetAiModuleSummaryRequestType requestType) throws Exception {
        return commonService.getAiModuleSummary(requestType);
    }



    //---------------------------- 以上为门票工作台-景点档案接口 ----------------------------
    @Override
    public GetDomesticMetricTrendDataResponseType getDomesticMetricTrendData(GetDomesticMetricTrendDataRequestType getDomesticMetricTrendDataRequestType) throws Exception {
        return metricDataBiz.getDomesticMetricTrendData(getDomesticMetricTrendDataRequestType);
    }


  @Override
    public GetDomesticMetricCardDataResponseType getDomesticMetricCardData(GetDomesticMetricCardDataRequestType getDomesticMetricCardDataRequestType) throws Exception {
        return metricDataBiz.getDomesticMetricCardData(getDomesticMetricCardDataRequestType);
    }    @Override
    public GetDomesticMetricSummaryDataResponseType getDomesticMetricSummaryData(GetDomesticMetricSummaryDataRequestType getDomesticMetricSummaryDataRequestType) throws Exception {
        return metricDataBiz.getDomesticMetricSummaryData(getDomesticMetricSummaryDataRequestType);
    }

    @Override
    public GetFirstPageDomesticMetricCardDataResponseType getFirstPageDomesticMetricCardData(GetFirstPageDomesticMetricCardDataRequestType getFirstPageDomesticMetricCardDataRequestType) throws Exception {
        return metricDataBiz.getFirstPageDomesticMetricCardData(getFirstPageDomesticMetricCardDataRequestType);
    }
    //---------------------------- 以上为仪表盘十四期项目内容 ----------------------------


}
