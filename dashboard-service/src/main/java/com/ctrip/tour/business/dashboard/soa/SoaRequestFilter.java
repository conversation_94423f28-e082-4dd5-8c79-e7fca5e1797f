package com.ctrip.tour.business.dashboard.soa;

import com.ctrip.framework.foundation.Foundation;
import com.ctrip.soa.caravan.web.filter.CrossDomainFilter;
import com.ctrip.soa.caravan.web.utils.VulDef;
import com.ctrip.tour.business.dashboard.tktBusiness.annotation.NotLoginRequired;
import com.ctrip.tour.business.dashboard.tktBusiness.checker.UserChecker;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.ctriposs.baiji.rpc.server.ServiceHost;
import com.ctriposs.baiji.rpc.server.filter.RequestFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import java.lang.annotation.Annotation;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@Slf4j
@Component
public class SoaRequestFilter implements RequestFilter {

    @Autowired
    private UserChecker userChecker;


    @Override
    public void apply(ServiceHost host, HttpRequestWrapper request, HttpResponseWrapper response) {
        //目前需要排除权限验证的方法有  checkHealth
        //SLB拉入时会访问checkHealth方法  因此权限控制排除checkHealth方法 否则会导致gateWay访问方式不可用
        //也可以用下面注释的代码做接口排除
        //        String operationName = request.operationName();
        //        if("checkhealth".equals(operationName)){
        //            return;
        //        }

        // 跨域
//        response.setHeader("Access-Control-Allow-Origin", "https://vendor.package.fat29.qa.nt.ctripcorp.com");
//        response.setHeader("Access-Control-Expose-Headers", "appname,content-type");
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        if ("OPTIONS".equals(request.httpMethod())) {
//            String accessControllRequestMethod = request.getHeader("Access-Control-Request-Method");
//            if (accessControllRequestMethod != null) {
//                response.setHeader("Access-Control-Allow-Methods", VulDef.securityRSHeaderInjection(accessControllRequestMethod));
//            }
//            String accessControllRequestHeaders = request.getHeader("Access-Control-Request-Headers");
//            if (accessControllRequestHeaders != null) {
//                response.setHeader("Access-Control-Allow-Headers", VulDef.securityRSHeaderInjection(accessControllRequestHeaders));
//            }
//            response.setHeader("Access-Control-Max-Age", "3600");
//            response.setStatus(200);
//            response.sendResponse();
//            return;
//        }

        String skipAuth = request.getHeader("skipAuth");
        if (StringUtils.equals(skipAuth, "true")) {
            MDC.put("skipAuth", "true");
            // 暂不return，后续会塞empCode
//            return;
        }

        MDC.put("metric", "-1");
        String appname = userChecker.getAppname(request);
        MDC.put("appname", appname);
        Annotation annotation = request.operationHandler().getMethod().getAnnotation(NotLoginRequired.class);
        if (annotation != null) {
            return;
        }
        switch (appname) {
            case "dashboardpc":
                checkDashboardPC(request, response);
                break;
            case "dashboardapp":
                checkDashboardAPP(request, response);
                break;
            default:
                setResponseStatus(response);
        }

    }

    /**
     * 校验仪表盘pc端登录权限
     *
     * @param request
     * @param response
     */
    private void checkDashboardPC(HttpRequestWrapper request, HttpResponseWrapper response) {
        //获取用户工号
        String casTicket = userChecker.getCasTicket(request);
        String empCode = userChecker.getEmpCode(casTicket);
        //获取语种
        userChecker.setVBKLocaleLang(request);
        //未获取到工号 返回异常403 前端跳转到登录页
        if (StringUtils.isBlank(empCode)) {
            // 如果跳过验证，直接走绿色通道
            if (StringUtils.equals(MDC.get("skipAuth"), "true")) {
                return;
            }
            setResponseStatus(response);
        }
    }


    /**
     * 校验仪表盘app端登录权限
     *
     * @param request
     * @param response
     */
    private void checkDashboardAppOrg(HttpRequestWrapper request, HttpResponseWrapper response) {
        String cticket = userChecker.getCticket(request);
        String empCode = userChecker.getEmpCode(cticket, "Offline");
        //获取语种
        userChecker.setAppLocaleLang(request);
        //未获取到工号  返回异常403 前端跳转到登录页
        if ("".equals(empCode)) {
            setResponseStatus(response);
        }
    }

    /**
     * 校验仪表盘app端登录权限
     *
     * @param request
     * @param response
     */
    private void checkDashboardAPP(HttpRequestWrapper request, HttpResponseWrapper response) {
        //获取用户工号
        String envName = Foundation.server().getEnvFamily().getName();
        String opTicket = "";
        if (isFatOrFwsOrUat(envName)) {
            opTicket = userChecker.getFwsOpTicket(request);
        } else if (isProd(envName)) {
            opTicket = userChecker.getOpTicket(request);
        } else {
            setResponseStatus(response);
        }
        String empCode = userChecker.getEmpCodeByOpTicket(opTicket);
        //获取语种
        userChecker.setAppLocaleLang(request);
        //未获取到工号，opticket校验失败，使用cticket兜底
        if (StringUtils.isBlank(empCode)) {
            // 如果跳过验证，直接走绿色通道，干掉cticket之后，下面三行要解开
//            if (StringUtils.equals(MDC.get("skipAuth"), "true")) {
//                return;
//            }
//            log.error("empCode is blank, opTicket is {}",opTicket);
            checkDashboardAPPByCTicket(request, response);
        }
    }

    /**
     * 使用cticket兜底校验仪表盘app端登录权限（用于2025.8.7-2025.8.20临时使用，后面会下线）
     *
     * @param request
     * @param response
     */
    private void checkDashboardAPPByCTicket(HttpRequestWrapper request, HttpResponseWrapper response) {
        String cticket = userChecker.getCticket(request);
        String empCode = userChecker.getEmpCode(cticket, "Offline");
        Map<String, Cookie> cookies = request.getCookies();
        cookies.remove("fwsopticket");
        cookies.remove("opticket");
        //获取语种
        userChecker.setAppLocaleLang(request);
        //未获取到工号  返回异常403 前端跳转到登录页
        if (StringUtils.isBlank(empCode)) {
            // 如果跳过验证，直接走绿色通道
            if (StringUtils.equals(MDC.get("skipAuth"), "true")) {
                return;
            }
            setResponseStatus(response);
        }
    }


    /**
     * 设置没有登录时的返回码状态
     *
     * @param response
     */
    private void setResponseStatus(HttpResponseWrapper response) {
        response.setStatus(403);
        response.sendResponse();
    }

    /**
     * 当前环境是否是测试环境
     *
     * @param envName
     * @return
     */
    private Boolean isFatOrFwsOrUat(String envName) {
        if (StringUtils.isBlank(envName)) {
            return false;
        }
        return StringUtils.equalsIgnoreCase(envName, "fat") ||
                StringUtils.equalsIgnoreCase(envName, "fws") ||
                StringUtils.equalsIgnoreCase(envName, "uat");
    }

    /**
     * 当前环境是否是生产环境
     * @param envName
     * @return
     */
    private Boolean isProd(String envName) {
        if (StringUtils.isBlank(envName)) {
            return false;
        }
        return StringUtils.equalsIgnoreCase(envName, "pro");
    }
}
