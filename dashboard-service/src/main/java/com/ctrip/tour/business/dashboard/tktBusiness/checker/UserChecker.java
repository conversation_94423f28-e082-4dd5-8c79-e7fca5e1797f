package com.ctrip.tour.business.dashboard.tktBusiness.checker;

import com.ctrip.framework.foundation.Foundation;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OrganizationInfoBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.HickWallLogUtil;
import com.ctrip.tour.business.dashboard.utils.RedisUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.tour.vendor.usersvc.soa.v1.service.VendorUserServiceClient;
import com.ctrip.tour.vendor.usersvc.soa.v1.service.type.GetSessionInfoByIdRequestType;
import com.ctrip.tour.vendor.usersvc.soa.v1.service.type.GetSessionInfoByIdResponseType;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import credis.java.client.CacheProvider;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@Component
@Slf4j
public class UserChecker {

    @Autowired
    private VendorUserServiceClient vendorUserServiceClient;

    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;

    @Autowired
    private RemoteConfig remoteConfig;

    private static final String SCENETYPE = "business_dashboard_empcode_";


    public String getEmpCode(String casTicket) {
        String empCode = "";
        GetSessionInfoByIdRequestType request = new GetSessionInfoByIdRequestType();
        request.setCasTicket(casTicket);
        GetSessionInfoByIdResponseType response;
        try {
            response = vendorUserServiceClient.getSessionInfoById(request);
        } catch (Exception e) {
            log.error("[vendorUserServiceClient]:getSessionInfoById调用出错，获取用户信息失败：", e); //NOSONAR
            HickWallLogUtil.logSend("tour.bi.business.dashboard.checkcookie.error.count");
            return empCode;
        }
        if (response.getSession() != null) {
            empCode = response.getSession().getEmpCode();
        }
        MDC.put("empCode", empCode);
        return empCode;
    }

    public String getEmpCodeByOpTicket(String opTicket) {
        String empCode = "";
        GetSessionInfoByIdRequestType request = new GetSessionInfoByIdRequestType();
        request.setCticket(opTicket);
        GetSessionInfoByIdResponseType response;
        try {
            response = vendorUserServiceClient.getSessionInfoById(request);
        } catch (Exception e) {
            log.error("[vendorUserServiceClient]:getSessionInfoById调用出错，获取用户信息失败：", e); //NOSONAR
            HickWallLogUtil.logSend("tour.bi.business.dashboard.checkcookie.error.count");
            return empCode;
        }
        if (response.getSession() != null) {
            empCode = response.getSession().getEmpCode();
        }
        MDC.put("empCode", empCode);
        return empCode;
    }

    /**
     * 根据cticket和partyType获取员工号
     *
     * @param cticket
     * @param partyType
     * @return
     */
    public String getEmpCode(String cticket,
                             String partyType) {
        String empCode = "";
        String empName = "";
        GetSessionInfoByIdRequestType request = new GetSessionInfoByIdRequestType();
        request.setPartyType(partyType);
        request.setCticket(cticket);
        GetSessionInfoByIdResponseType response;
        try {
            response = vendorUserServiceClient.getSessionInfoById(request);
        } catch (Exception e) {
            log.error("[vendorUserServiceClient]:getSessionInfoById调用出错，获取用户信息失败：", e); //NOSONAR
            HickWallLogUtil.logSend("tour.bi.business.dashboard.checkcookie.error.count");
            return empCode;
        }
        if (response.getSession() != null) {
            empCode = response.getSession().getEmpCode();
            empName = response.getSession().getEidName();
        }
        MDC.put("empCode", empCode);
        MDC.put("empName", empName);
        return empCode;
    }


    /**
     * 从request中获取casTicket
     * @param httpRequestWrapper
     * @return
     */
    public String getCasTicket(HttpRequestWrapper httpRequestWrapper) {
        String envName = Foundation.server().getEnvFamily().getName();
        String cookieName = envName + "_cas_principal";
        return getCookieValue(httpRequestWrapper,cookieName);
    }

    /**
     * 从request中获取cticket
     * @param httpRequestWrapper
     * @return
     */
    public String getCticket(HttpRequestWrapper httpRequestWrapper){
        return getCookieValue(httpRequestWrapper,"cticket");
    }

    /**
     * 从request中获取fwsopticket
     * @param httpRequestWrapper
     * @return
     */
    public String getFwsOpTicket(HttpRequestWrapper httpRequestWrapper){
        return getCookieValue(httpRequestWrapper,"fwsopticket");
    }

    /**
     * 从request中获取opticket
     * @param httpRequestWrapper
     * @return
     */
    public String getOpTicket(HttpRequestWrapper httpRequestWrapper){
        return getCookieValue(httpRequestWrapper,"opticket");
    }


    /**
     * 从request中获取vbk-locale-lang
     * @param httpRequestWrapper
     * @return
     */
    public void setVBKLocaleLang(HttpRequestWrapper httpRequestWrapper) {
        getCookieValue(httpRequestWrapper, "vbk-locale-lang");
    }

    /**
     * 从request中获取app端locale
     * @param httpRequestWrapper
     * @return
     */
    public void setAppLocaleLang(HttpRequestWrapper httpRequestWrapper) {
        String cookieValue = getCookieValue(httpRequestWrapper, "locale");
        log.debug("The locale in the cookie on the app side is:{}",cookieValue);
        MDC.put("vbk-locale-lang",cookieValue);
    }

    /**
     * 从request的header中获取appname(小写)
     * @param httpRequestWrapper
     * @return
     */
    public String getAppname(HttpRequestWrapper httpRequestWrapper){
        return Optional.ofNullable(httpRequestWrapper.getHeader("appname"))
                .orElse("")
                .toLowerCase();
    }

    /**
     * 从request中获取对应cookie的值 如果找不到 返回空字符串
     * @param httpRequestWrapper
     * @return
     */
    private String getCookieValue(HttpRequestWrapper httpRequestWrapper,
                                  String cookieName) {
        String cookieValue = "";
        Map<String, Cookie> cookies = httpRequestWrapper.getCookies();
        for (Map.Entry<String, Cookie> entry : cookies.entrySet()) {
            String key = entry.getKey();
            if (cookieName.equals(key)) {
                cookieValue = entry.getValue().getValue();
                break;
            }
        }
        MDC.put(cookieName, cookieValue);
        return cookieValue;
    }

    public void fastCheckInputDomainName(String domainName) throws SQLException {
        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        String admin = remoteConfig.getExternalConfig("admin");
        //老季默认是全局用户 不校验了
        if (admin.equals(empCode)) {
            return;
        }
        CacheProvider provider = RedisUtil.getProvider(remoteConfig.getConfigValue("profileRedisName"));
        String redisKey = RedisUtil.getKey(SCENETYPE, empCode);

        //如果当前登录用户可以查看选中的用户 直接返回
        if (RedisUtil.sismember(provider, redisKey, domainName)) {
            return;
        }

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);
        if (employeeInfo == null) {
            throw new InputArgumentException("输入的非法域账号为:" + domainName);
        }

        //本人一定可以查看本人的数据
        if (empCode.equals(employeeInfo.getEmpCode())) {
            RedisUtil.sadd(provider, redisKey, domainName, 3600L);
            return;
        }
        //如果查看的不是本人数据
        //则选中用户与当前用户一定存在某种上下级关系  尝试寻找
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(empCode);
        OrganizationInfoBo bo = new OrganizationInfoBo();
        List<String> childOrgIdList = bo.getChildOrgIdList(organizationInfoList);
        String orgIdPath = employeeInfo.getOrgIdPath();
        for(String childOrgId : childOrgIdList){
            if(orgIdPath.contains(childOrgId)){
                RedisUtil.sadd(provider, redisKey, domainName, 3600L);
                return;
            }
        }
        throw new InputArgumentException("输入的非法域账号为:" + domainName);
    }

}
