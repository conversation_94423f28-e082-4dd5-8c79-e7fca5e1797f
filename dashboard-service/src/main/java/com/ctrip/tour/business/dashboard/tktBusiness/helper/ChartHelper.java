package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.BiGmvProfitTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.CdmOrdTtdDashboardExamLevelPerfDfBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic567WeakNessBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.DomesticMetricParamBean;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
public class ChartHelper {


    //将返回数据进行汇总  并填充进指标卡

    /**
     * @param rawResultList 原始数据
     * @param dimList       需要进行加和计算的数据列
     * @param dimMap        结果数据
     */
    public static void fillOverallDimMapWithSum(List<List<Object>> rawResultList,
                                                List<String> dimList,
                                                Map<String, Double> dimMap) {
        int size = dimList.size();
        for (List<Object> rowResult : rawResultList) {
            for (int i = 0; i < size; i++) {
                Double currentValue = dimMap.getOrDefault(dimList.get(i), 0d);
                Object value = rowResult.get(i);
                if (GeneralUtil.isNotEmpty(value)) {
                    currentValue += Double.valueOf(String.valueOf(value));
                }
                dimMap.put(dimList.get(i), currentValue);
            }
        }
    }


    //直接填充指标卡数据

    /**
     * @param rawResultList 原始数据
     * @param dimList       原始数据对应的dim列表
     * @param dimMap        结果map
     */
    public static void fillOverallDimMap(List<List<Object>> rawResultList,
                                         List<String> dimList,
                                         Map<String, Double> dimMap) {
        int size = dimList.size();
        for (List<Object> rowResult : rawResultList) {
            for (int i = 0; i < size; i++) {
                Object value = rowResult.get(i);
                if (value != null) {
                    dimMap.put(dimList.get(i), Double.valueOf(String.valueOf(value)));
                }
            }
        }
    }

    public static void fillOverallDimMapWithMultiple(List<List<Object>> rawResultList,
                                                     List<String> dimList,
                                                     Map<String, Double> dimMap) {
        int size = dimList.size();
        for (List<Object> rowResult : rawResultList) {
            for (int i = 0; i < size; i++) {
                Object value = rowResult.get(i);
                if (value != null) {
                    dimMap.put(dimList.get(i), Double.valueOf(String.valueOf(value)));
                }
            }
        }
    }

    //填充同比所需数据的条件map

    /**
     * @param inMap         当前数据的条件map
     * @param year          年
     * @param lastYearInMap 去年数据的条件map
     * @param _2019InMap    2019年数据的条件map
     * @throws ParseException
     */
    public static void fillPopInMap(Map<String, List<String>> inMap,
                                    String year,
                                    Map<String, List<String>> lastYearInMap,
                                    Map<String, List<String>> _2019InMap) throws ParseException {
        String lastYear = DateUtil.getLastYear(year);
        for (Map.Entry<String, List<String>> entry : inMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            if ("year".equals(key)) {
                lastYearInMap.put(key, Lists.newArrayList(lastYear));
                _2019InMap.put(key, Lists.newArrayList("2019"));
            } else {
                lastYearInMap.put(key, valueList);
                _2019InMap.put(key, valueList);
            }
        }
    }

    //将环比数据填充到结果map中

    /**
     *
     * @param dimMap 结果map
     * @param dalHints 存储异步数据
     * @param momDalHints 存储异步数据
     * @param dimList 数据对应的dim列表
     * @param type 环比类型
     * @throws Exception
     */
//    public static void makeUpMetricCardMomData(Map<String, Double> dimMap,
//                                               DalHints dalHints,
//                                               DalHints momDalHints,
//                                               List<String> dimList,
//                                               String type) throws Exception {
//        if ("7days".equals(type) || "30days".equals(type)) {
//            List<List<Object>> rawDataList = dalHints.getListResult();
//            List<List<Object>> momRawDataList = momDalHints.getListResult();
//            for (int i = 0; i < dimList.size(); i++) {
//                Object rawValue = rawDataList.get(0).get(i);
//                Object momRawValue = momRawDataList.get(0).get(i);
//                if (rawValue != null && momRawValue != null) {
//                    Double value = Double.valueOf(String.valueOf(rawValue));
//                    Double momValue = Double.valueOf(String.valueOf(momRawValue));
//                    if (GeneralUtil.isValidDivide(value, momValue)) {
//                        dimMap.put(dimList.get(i) + "_" + type, value / momValue - 1);
//                    }
//                }
//            }
//        }
//        if ("lastmonth".equals(type) || "lastquarter".equals(type)) {
//            List<List<Object>> momRawDataList = momDalHints.getListResult();
//            for (int i = 0; i < dimList.size(); i++) {
//                Object momRawValue = momRawDataList.get(0).get(i);
//                if (momRawValue != null) {
//                    Double value = dimMap.get(dimList.get(i));
//                    Double momValue = Double.valueOf(String.valueOf(momRawValue));
//                    if (GeneralUtil.isValidDivide(value, momValue)) {
//                        dimMap.put(dimList.get(i) + "_" + type, value / momValue - 1);
//                    }
//                }
//            }
//        }
//    }


    //将环比数据填充到结果map中
    //版本2 后续会替代原始版本

    /**
     * @param dimMap      结果map
     * @param dalHints    存储异步数据
     * @param momDalHints 存储异步数据
     * @param dimList     数据对应的dim列表
     * @param type        环比类型
     * @throws Exception
     */
    public static void makeUpMetricCardMomDataV2(Map<String, Double> dimMap,
                                                 DalHints dalHints,
                                                 DalHints momDalHints,
                                                 List<String> dimList,
                                                 String type) throws Exception {
        List<List<Object>> rawDataList = dalHints.getListResult();
        List<List<Object>> momRawDataList = momDalHints.getListResult();
        for (int i = 0; i < dimList.size(); i++) {
            Object rawValue = rawDataList.get(0).get(i);
            Object momRawValue = momRawDataList.get(0).get(i);
            if (rawValue != null && momRawValue != null) {
                Double value = Double.valueOf(String.valueOf(rawValue));
                Double momValue = Double.valueOf(String.valueOf(momRawValue));
                if (GeneralUtil.isNotEmpty(value)) {
                    dimMap.put(dimList.get(i) + "_" + type + "_fenzi_value", value);
                }
                if (GeneralUtil.isNotEmpty(momValue)) {
                    dimMap.put(dimList.get(i) + "_" + type + "_fenmu_value", momValue);
                }
                if (GeneralUtil.isValidDivide(value, momValue)) {
                    dimMap.put(dimList.get(i) + "_" + type, value / momValue - 1);
                }
            }
        }
    }

    //将环比数据填充到返回结果中

    /**
     * @param tableDataItemList 返回结果
     * @param dalHints          当前结果所在结构
     * @param momDalHints       环比结果所在结构
     * @param dimList           dim的list
     * @param groupTagList      拆分维度的list
     * @param suffix            dim的后缀
     * @throws Exception
     */
    public static void makeUpTableMomData(List<TableDataItem> tableDataItemList,
                                          DalHints dalHints,
                                          DalHints momDalHints,
                                          List<String> dimList,
                                          List<String> groupTagList,
                                          String suffix) throws Exception {
        makeUpTableMomData(tableDataItemList,
                dalHints.getListResult(),
                momDalHints.getListResult(),
                dimList,
                groupTagList,
                suffix);
    }


    //将环比数据填充到返回结果中

    /**
     * @param tableDataItemList 返回结果
     * @param rawDataList       当前结果
     * @param momRawDataList    环比结果
     * @param dimList           dim的list
     * @param groupTagList      拆分维度的list
     * @param suffix            dim的后缀
     * @throws Exception
     */
    public static void makeUpTableMomData(List<TableDataItem> tableDataItemList,
                                          List<List<Object>> rawDataList,
                                          List<List<Object>> momRawDataList,
                                          List<String> dimList,
                                          List<String> groupTagList,
                                          String suffix) {
        Map<String, Double> rawMap = new HashMap<>();
        Map<String, Double> momRawMap = new HashMap<>();
        int size = groupTagList.size();
        for (List<Object> rowResult : rawDataList) {
            //合并拆分维度
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < size; i++) {
                sb.append(rowResult.get(i)).append(":");
            }
            String drillDown = sb.toString();
            for (int i = size; i < rowResult.size(); i++) {
                String dim = dimList.get(i - size);
                Object objectValue = rowResult.get(i);
                if (objectValue != null) {
                    Double value = Double.valueOf(String.valueOf(objectValue));
                    rawMap.put(drillDown + dim, value);
                }
            }
        }
        for (List<Object> rowResult : momRawDataList) {
            //合并拆分维度
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < size; i++) {
                sb.append(rowResult.get(i)).append(":");
            }
            String drillDown = sb.toString();
            for (int i = size; i < rowResult.size(); i++) {
                String dim = dimList.get(i - size);
                Object objectValue = rowResult.get(i);
                if (objectValue != null) {
                    Double value = Double.valueOf(String.valueOf(objectValue));
                    momRawMap.put(drillDown + dim, value);
                }
            }
        }
        for (TableDataItem item : tableDataItemList) {
            Map<String, String> fieldMap = item.getFieldMap();
            Map<String, Double> dimMap = item.getDimMap();
            StringBuilder sb = new StringBuilder();
            for (String groupTag : groupTagList) {
                sb.append(fieldMap.get(groupTag)).append(":");
            }
            String drillDown = sb.toString();
            for (String dim : dimList) {
                Double value = rawMap.get(drillDown + dim);
                Double momValue = momRawMap.get(drillDown + dim);
                if (GeneralUtil.isValidDivide(value, momValue)) {
                    dimMap.put(dim + suffix, value / momValue - 1);
                }
            }
        }
    }

    //对于季度指标  获取趋势线时
    //如果是月度进展
    // 计算真正的月度进展  需要累计
    // 4月进展统计4月
    // 5月进展统计4+5月
    // 6月进展统计4+5+6月  以此类推
    //如果是季度进展 则直接填充返回

    /***
     *
     * @param dimMap  存储结果数据供下一步使用
     * @param drillDownSet 存储当前数据中除时间外的下钻维度  下钻时使用
     * @param rawResultList 原始数据  由数据库直接获得
     * @param dateType  日期类型  枚举值为month和quarter
     * @param groupTagList 下钻维度列表
     * @param reachDimList dim列表
     */
    public static void setTrendlineActualReach(Map<String, Double> dimMap,
                                               Set<String> drillDownSet,
                                               List<List<Object>> rawResultList,
                                               String dateType,
                                               List<String> groupTagList,
                                               List<String> reachDimList) {
        Map<String, Double> tempMap = new HashMap<>();
        int size = groupTagList.size();
        for (List<Object> rowResult : rawResultList) {
            //拆分维度的第一个参数固定为时间参数
            String time = String.valueOf(rowResult.get(0));
            //合并后续拆分维度
            StringBuilder sb = new StringBuilder(":");
            for (int j = 1; j < size; j++) {
                sb.append(rowResult.get(j)).append(":");
            }
            String drillDown = sb.toString();
            drillDownSet.add(drillDown);
            for (int i = size; i < rowResult.size(); i++) {
                String dim = reachDimList.get(i - size);
                Object objectValue = rowResult.get(i);
                //由于这里会取到未来的值 所以理论上会有null出现 处理一下
                Double value = 0d;
                if (objectValue != null) {
                    value = Double.valueOf(String.valueOf(objectValue));
                }
                tempMap.put(time + drillDown + dim, value);
            }
        }
        if ("quarter".equals(dateType)) {
            dimMap.putAll(tempMap);
        } else {
            for (Map.Entry<String, Double> entry : tempMap.entrySet()) {
                String key = entry.getKey();
                String[] keyArray = key.split(":");
                String currentMonth = keyArray[0];
                List<String> monthList = DateUtil.getMonthList(currentMonth, false);
                Double value = 0d;
                for (String month : monthList) {
                    value += tempMap.getOrDefault(month + key.substring(2), 0d);
                }
                dimMap.put(key, value);
            }
        }
    }


    public static void setMonthlyMetricTrendlineActualReach(Map<String, Double> dimMap,
                                                            Set<String> drillDownSet,
                                                            List<List<Object>> rawResultList,
                                                            List<String> groupTagList,
                                                            List<String> reachDimList) {
        setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, rawResultList, groupTagList, reachDimList, true);
    }


    //填充月度指标趋势线数据

    /***
     *
     * @param dimMap  存储结果数据供下一步使用
     * @param drillDownSet 存储当前数据中除时间外的下钻维度  下钻时使用
     * @param rawResultList 原始数据  由数据库直接获得
     * @param groupTagList 下钻维度列表
     * @param reachDimList dim列表
     * @param needDefaultValue 是否需要默认值
     */
    public static void setMonthlyMetricTrendlineActualReach(Map<String, Double> dimMap,
                                                            Set<String> drillDownSet,
                                                            List<List<Object>> rawResultList,
                                                            List<String> groupTagList,
                                                            List<String> reachDimList,
                                                            Boolean needDefaultValue) {
        int size = groupTagList.size();
        for (List<Object> rowResult : rawResultList) {
            //拆分维度的第一个参数固定为时间参数
            String time = String.valueOf(rowResult.get(0));
            //合并后续拆分维度
            StringBuilder sb = new StringBuilder(":");
            for (int j = 1; j < size; j++) {
                sb.append(rowResult.get(j)).append(":");
            }
            String drillDown = sb.toString();
            drillDownSet.add(drillDown);
            for (int i = size; i < rowResult.size(); i++) {
                String dim = reachDimList.get(i - size);
                Object objectValue = rowResult.get(i);
                //由于这里会取到未来的值 所以理论上会有null出现 处理一下
                Double value = needDefaultValue ? 0d : null;
                if (objectValue != null) {
                    value = Double.valueOf(String.valueOf(objectValue));
                }
                dimMap.put(time + drillDown + dim, value);
            }
        }
    }


    //对于季度指标
    //将查询出的季度目标转化为合适的形式
    //季度原始  或者按月度累加

    /***
     *
     * @param dimMap   存储结果数据供下一步使用
     * @param drillDownSet 存储当前数据中除时间外的下钻维度  下钻时使用
     * @param rawResultList 原始数据  从数据库中查出
     * @param dateType 日期类型
     * @param groupTagList  下钻维度列表
     * @param targetDimList  dim列表
     */
    public static void setTrendlineActualTarget(Map<String, Double> dimMap,
                                                Set<String> drillDownSet,
                                                List<List<Object>> rawResultList,
                                                String dateType,
                                                List<String> groupTagList,
                                                List<String> targetDimList) {
        int size = groupTagList.size();
        for (List<Object> rowResult : rawResultList) {
            //拆分维度的第一个参数固定为时间参数
            String quarter = String.valueOf(rowResult.get(0));
            List<String> monthListOfQuarter = DateUtil.getMonthListOfQuarter(quarter);
            //合并后续拆分维度
            StringBuilder sb = new StringBuilder(":");
            for (int j = 1; j < size; j++) {
                sb.append(rowResult.get(j)).append(":");
            }
            for (int i = size; i < rowResult.size(); i++) {
                String dim = targetDimList.get(i - size);
                Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
                String drillDown = sb.toString();
                drillDownSet.add(drillDown);
                if ("month".equals(dateType)) {
                    for (String month : monthListOfQuarter) {
                        dimMap.put(month + drillDown + dim, value);
                    }
                } else {
                    dimMap.put(quarter + drillDown + dim, value);
                }
            }
        }
    }


    //填充柱状图或者折线图数据

    /***
     *
     * @param year  年
     * @param timeList 趋势线有效的时间范围
     * @param dimMap 存储原始数据
     * @param trendLineDetailInfoList 结果数据
     * @param typeMap  趋势线元数据
     */
    public static void fillLineChartTrendLineData(String year,
                                                  List<String> timeList,
                                                  Map<String, Double> dimMap,
                                                  List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                  Map<String, String> typeMap) {
        for (Map.Entry<String, String> entry : typeMap.entrySet()) {
            TrendLineDetailInfo trendLineDetailInfo = new TrendLineDetailInfo();
            trendLineDetailInfoList.add(trendLineDetailInfo);
            String originDim = entry.getKey();
            trendLineDetailInfo.setDim(originDim);
            trendLineDetailInfo.setType(entry.getValue());
            List<TrendLineDataItem> trendLineDataItemList = new ArrayList<>();
            trendLineDetailInfo.setTrendLineDataItemList(trendLineDataItemList);
            String[] array = originDim.split("\\|");
            for (String time : timeList) {
                TrendLineDataItem item = new TrendLineDataItem();
                if (GeneralUtil.isEmpty(year)) {
                    item.setTime(time);
                } else {
                    item.setTime(year + "-" + time);
                }

                if (DimHelper.isSpecialDim(originDim)) {
                    Double value = DimHelper.getSpecialDimValue(originDim, time + ":", dimMap, null);
                    if (!GeneralUtil.isEmpty(value)) {
                        item.setValue(value);
                    }
                } else {
                    if (array.length == 1) {
                        Double value = dimMap.get(time + ":" + originDim);
                        if (!GeneralUtil.isEmpty(value)) {
                            item.setValue(value);
                        }
                    } else {
                        Double leftnum = dimMap.get(time + ":" + array[0]);
                        Double rightnum = dimMap.get(time + ":" + array[1]);
                        //运算符
                        String operator = array[2];
                        Double value = GeneralUtil.getComplexResult(leftnum, rightnum, operator);
                        if (!GeneralUtil.isEmpty(value)) {
                            item.setValue(value);
                        }
                        //把复合指标重新写入dimMap 有些特殊需要这个值参与后续计算
                        dimMap.put(time + ":" + originDim, value);
                    }
                }
                trendLineDataItemList.add(item);
            }

        }
    }


    //填充柱状图或者折线图环比数据
    /***
     *
     * @param year  年
     * @param timeList 趋势线有效的时间范围
     * @param dimMap 存储原始数据
     * @param popDimMapList 存储环比原始数据
     * @param trendLineDetailInfoList 结果数据
     * @param typeMap  趋势线元数据
     */
//    public static void fillLineChartTrendLinePopData(String year,
//                                                     List<String> timeList,
//                                                     Map<String, Double> dimMap,
//                                                     List<Map<String, Double>> popDimMapList,
//                                                     List<TrendLineDetailInfo> trendLineDetailInfoList,
//                                                     Map<String, String> typeMap) {
//        int i = 0;
//        for (Map.Entry<String, String> entry : typeMap.entrySet()) {
//            Map<String, Double> popDimMap = popDimMapList.get(i++);
//            TrendLineDetailInfo trendLineDetailInfo = new TrendLineDetailInfo();
//            trendLineDetailInfoList.add(trendLineDetailInfo);
//            String originDim = entry.getKey();
//            trendLineDetailInfo.setDim(originDim);
//            trendLineDetailInfo.setType(entry.getValue());
//            List<TrendLineDataItem> trendLineDataItemList = new ArrayList<>();
//            trendLineDetailInfo.setTrendLineDataItemList(trendLineDataItemList);
//            for (String time : timeList) {
//                TrendLineDataItem item = new TrendLineDataItem();
//                item.setTime(year + "-" + time);
//                Double value = DimHelper.getSpecialDimValue(originDim, time + ":", dimMap, popDimMap);
//                item.setValue(value);
//                trendLineDataItemList.add(item);
//            }
//
//        }
//    }


    //填充单个柱状图或者折线图环比数据
    /***
     *
     * @param year  年
     * @param timeList 趋势线有效的时间范围
     * @param dimMap 存储原始数据
     * @param popDimMap 存储环比原始数据
     * @param trendLineDetailInfoList 结果数据
     * @param lineDim  趋势线名称
     * @param lineType 线型
     */
//    public static void fillLineChartTrendLinePopData(String year,
//                                                     List<String> timeList,
//                                                     Map<String, Double> dimMap,
//                                                     Map<String, Double> popDimMap,
//                                                     List<TrendLineDetailInfo> trendLineDetailInfoList,
//                                                     String lineDim,
//                                                     String lineType) {
//        TrendLineDetailInfo trendLineDetailInfo = new TrendLineDetailInfo();
//        trendLineDetailInfoList.add(trendLineDetailInfo);
//        trendLineDetailInfo.setDim(lineDim);
//        trendLineDetailInfo.setType(lineType);
//        List<TrendLineDataItem> trendLineDataItemList = new ArrayList<>();
//        trendLineDetailInfo.setTrendLineDataItemList(trendLineDataItemList);
//        for (String time : timeList) {
//            TrendLineDataItem item = new TrendLineDataItem();
//            item.setTime(year + "-" + time);
//            Double value = DimHelper.getSpecialDimValue(lineDim, time + ":", dimMap, popDimMap);
//            item.setValue(value);
//            trendLineDataItemList.add(item);
//        }
//    }
//


    //填充下钻柱状图或者折线图数据

    /***
     *
     * @param year  年
     * @param timeList 趋势线有效的时间范围
     * @param dimMap 存储原始数据
     * @param trendLineDetailInfoList 结果数据
     * @param typeMap  趋势线元数据
     * @param drillDownSet 有效的中间下钻中间维度
     */
    public static void fillLineChartTrendLineDataWithDrillDown(String year,
                                                               List<String> timeList,
                                                               Map<String, Double> dimMap,
                                                               List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                               Map<String, String> typeMap,
                                                               Set<String> drillDownSet,
                                                               Boolean needDefaultValue) {
        for (Map.Entry<String, String> entry : typeMap.entrySet()) {
            for (String drillDown : drillDownSet) {
                TrendLineDetailInfo trendLineDetailInfo = new TrendLineDetailInfo();
                trendLineDetailInfoList.add(trendLineDetailInfo);
                String originDim = entry.getKey();
                trendLineDetailInfo.setDim(originDim);
                trendLineDetailInfo.setType(entry.getValue());
                List<TrendLineDataItem> trendLineDataItemList = new ArrayList<>();
                trendLineDetailInfo.setTrendLineDataItemList(trendLineDataItemList);
                String[] array = originDim.split("\\|");
                for (String time : timeList) {
                    TrendLineDataItem item = new TrendLineDataItem();
                    item.setName(drillDown.substring(1, drillDown.length() - 1));
                    if (GeneralUtil.isEmpty(year)) {
                        item.setTime(time);
                    } else {
                        item.setTime(year + "-" + time);
                    }
                    if (DimHelper.isSpecialDim(originDim)) {
                        Double value = DimHelper.getSpecialDimValue(originDim, time + drillDown, dimMap, null);
                        item.setValue(value);
                    } else {
                        if (array.length == 1) {
                            Double value = dimMap.get(time + drillDown + originDim);
                            if (needDefaultValue && GeneralUtil.isEmpty(value)) {
                                value = 0d;
                            }
                            item.setValue(value);
                        } else {
                            Double leftnum = dimMap.get(time + drillDown + array[0]);
                            Double rightnum = dimMap.get(time + drillDown + array[1]);
                            //运算符
                            String operator = array[2];
                            item.setValue(GeneralUtil.getComplexResult(leftnum, rightnum, operator, needDefaultValue));
                        }
                    }
                    trendLineDataItemList.add(item);
                }
            }
        }
    }


    //填充有关联数据的普通表格数据
    //适用于初始化填充

    /***
     *
     * @param tableDataItemList  存储最后数据
     * @param reachList 主数据
     * @param targetList 关联数据
     * @param groupTagList 下钻相关字段
     * @param targetDimList 关联数据的指标
     * @param reachDimList 主数据对应的指标
     */
    public static void fillCommmonTableData(List<TableDataItem> tableDataItemList,
                                            List<String> groupTagList,
                                            List<String> reachDimList,
                                            List<String> targetDimList,
                                            List<List<Object>> reachList,
                                            List<List<Object>> targetList) {
        Map<String, TableDataItem> tableDataItemMap = new HashMap<>();
        for (List<Object> rowResult : reachList) {
            TableDataItem item = new TableDataItem();
            tableDataItemList.add(item);
            Map<String, String> fieldMap = new HashMap<>();
            Map<String, Double> dimMap = new HashMap<>();
            item.setFieldMap(fieldMap);
            item.setDimMap(dimMap);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < groupTagList.size(); i++) {
                String groupTag = groupTagList.get(i);
                String groupTagValue = String.valueOf(rowResult.get(i));
                sb.append(groupTagValue).append(":");
                fieldMap.put(groupTag, groupTagValue);
            }
            tableDataItemMap.put(sb.toString(), item);
            for (int i = groupTagList.size(); i < rowResult.size(); i++) {
                Object rawValue = rowResult.get(i);
                if (GeneralUtil.isNotEmpty(rawValue)) {
                    Double value = Double.valueOf(String.valueOf(rawValue));
                    dimMap.put(reachDimList.get(i - groupTagList.size()), value);
                }
            }
        }
        for (List<Object> rowResult : targetList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < groupTagList.size(); i++) {
                String groupTagValue = String.valueOf(rowResult.get(i));
                sb.append(groupTagValue).append(":");
            }
            TableDataItem item = tableDataItemMap.get(sb.toString());
            //由于是按进展排序 没有找到进展 直接跳过
            if (item == null) {
                continue;
            }
            Map<String, Double> dimMap = item.getDimMap();
            for (int i = groupTagList.size(); i < rowResult.size(); i++) {
                Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
                dimMap.put(targetDimList.get(i - groupTagList.size()), value);
            }
        }
    }


    //填充有关联数据的普通表格数据
    //适用于初始化填充
    //版本v2
    //多个原始结果的关联维度是targetTagList

    /***
     *
     * @param tableDataItemList  存储最后数据
     * @param reachTagList 主数据的维度
     * @param targetTagList 关联数据的维度(同时用作和主数据的关联)
     * @param reachList 主数据
     * @param targetList 关联数据
     * @param targetDimList 关联数据的指标
     * @param reachDimList 主数据对应的指标
     */
    public static void fillCommmonTableDataV2(List<TableDataItem> tableDataItemList,
                                              List<String> currentTagList,
                                              List<String> targetTagList,
                                              List<String> currentMetricList,
                                              List<String> targetMetricList,
                                              List<List<Object>> currentList,
                                              List<List<Object>> targetList) {
        Map<String, List<TableDataItem>> tableDataItemListMap = new HashMap<>();
        for (List<Object> rowResult : currentList) {
            TableDataItem item = new TableDataItem();
            tableDataItemList.add(item);
            Map<String, String> fieldMap = new HashMap<>();
            Map<String, Double> dimMap = new HashMap<>();
            item.setFieldMap(fieldMap);
            item.setDimMap(dimMap);
            for (int i = 0; i < currentTagList.size(); i++) {
                String groupTag = currentTagList.get(i);
                String groupTagValue = String.valueOf(rowResult.get(i));
                fieldMap.put(groupTag, groupTagValue);
            }
            for (int i = currentTagList.size(); i < rowResult.size(); i++) {
                Object rawValue = rowResult.get(i);
                if (GeneralUtil.isNotEmpty(rawValue)) {
                    Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
                    dimMap.put(currentMetricList.get(i - currentTagList.size()), value);
                }
            }
        }
        for (TableDataItem item : tableDataItemList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < targetTagList.size(); i++) {
                Map<String, String> fieldMap = item.getFieldMap();
                String groupTag = targetTagList.get(i);
                String groupTagValue = fieldMap.get(groupTag);
                sb.append(groupTagValue).append(":");
            }
            List<TableDataItem> originItemList = tableDataItemListMap.getOrDefault(sb.toString(), new ArrayList<>());
            originItemList.add(item);
            tableDataItemListMap.put(sb.toString(), originItemList);
        }
        for (List<Object> rowResult : targetList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < targetTagList.size(); i++) {
                String groupTagValue = String.valueOf(rowResult.get(i));
                sb.append(groupTagValue).append(":");
            }
            List<TableDataItem> originItemList = tableDataItemListMap.get(sb.toString());
            //由于是按进展排序 没有找到进展 直接跳过
            if (originItemList == null) {
                continue;
            }
            for (TableDataItem item : originItemList) {
                Map<String, Double> dimMap = item.getDimMap();
                for (int i = targetTagList.size(); i < rowResult.size(); i++) {
                    Object rawValue = rowResult.get(i);
                    if (GeneralUtil.isNotEmpty(rawValue)) {
                        Double value = Double.valueOf(String.valueOf(rawValue));
                        dimMap.put(targetMetricList.get(i - targetTagList.size()), value);
                    }
                }
            }
        }
    }

    public static void buildBus1And2PlayAllDomesticTableData(List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult,
                                                             List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult,
                                                             List<BiGmvProfitTargetBO> targetResult,
                                                             List<String> tagList,
                                                             List<DomesticTableData> tableDataItemList,
                                                             String metric, String field, List<String> headerList) {
        Map<String, CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearValueMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : lastYearResult) {
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            String key = buildDomesticTableMapKey(field, bean);
                lastYearValueMap.put(key, bean);
        }
        Double zero=new Double(0);
        Map<String, Double> targetValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(targetResult)) {
            for (BiGmvProfitTargetBO bean : targetResult) {
                StringBuilder key = new StringBuilder();
                switch (field) {
                    case "region_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        break;
                    case "province_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        break;
                    case "examinee":
                        key.append(bean.getExaminee());
                        break;
                    case "viewspotid":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        key.append(bean.getVstId()).append(":");
                        key.append(bean.getVstName()).append(":");
                        key.append(bean.getExaminee()).append(":");
                        break;
                }
                switch (metric) {
                    case "1":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtIncome().toString()));
                        break;
                    case "2":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtProfit().toString()));
                        break;
                }
            }
        }

        Map<String, DomesticTableData> tableDataItemMap = new HashMap<>();
        for (DomesticTableData row : tableDataItemList) {
            String key = buildTableItemMapKey(field, row);
            tableDataItemMap.put(key, row);
        }
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : currentResult) {
            String key = buildDomesticTableMapKey(field, bean);
            DomesticTableData row = null;
            if ("examinee".equalsIgnoreCase(field)) {
                row = getRowByKey(tableDataItemMap, key);
            } else {
                row = tableDataItemMap.get(key);
            }
            if (row == null) {
                continue;
            }
            //已有数据set到活动
            //已有数据+现有数据，复制到已有数据
            //当前数据set到日游字段
            Double popValueAct = row.getCompleteValue() / (row.getYoyValue() + 1);
            row.setActCompleteValue(row.getCompleteValue());
            row.setActTargetValue(row.getTargetValue());
            row.setActCompleteRate(row.getCompleteRate());
            row.setActYoyValue(row.getYoyValue());

            if ("2".equalsIgnoreCase(metric)) {
                row.setActInnerProfitValue(row.getInnerProfitValue());
                row.setActOuterProfitValue(row.getOuterProfitValue());
                row.setOdfInnerProfitValue(bean.getTtdSysInnerProfit());
                row.setOdfOuterProfitValue(bean.getTtdSysOuterProfit());
                row.setInnerProfitValue(row.getActInnerProfitValue() + row.getOdfInnerProfitValue());
                row.setOuterProfitValue(row.getActOuterProfitValue() + row.getOdfOuterProfitValue());
            }

//            row.setTargetValue(Double.valueOf(0));
            CdmOrdTtdDashboardExamLevelPerfDfBO lastBo = lastYearValueMap.get(key);
            Double popValue = chooseValueByDomesticMetric(lastBo, metric);
            Double value = row.getCompleteValue();
            if (GeneralUtil.isValidDivide(value, popValue)) {
                row.setOdfYoyValue(value / popValue - 1);
                Double totalPopValue = popValueAct + popValue;
                row.setYoyValue(row.getCompleteValue() / totalPopValue - 1);
            }
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            row.setOdfCompleteValue(valueBean);
            row.setOdfTargetValue(zero);
            if (targetValueMap.containsKey(key)) {
                row.setOdfTargetValue(targetValueMap.get(key));
                row.setOdfCompleteRate(row.getOdfCompleteValue() / row.getOdfTargetValue());
            }
            row.setCompleteValue(row.getCompleteValue() + valueBean);
            row.setTargetValue(row.getActTargetValue() + row.getOdfTargetValue());
            if (!zero.equals(row.getTargetValue())) {
                row.setCompleteRate(row.getCompleteValue() / row.getTargetValue());
            }

        }
    }

    private static DomesticTableData getRowByKey(Map<String, DomesticTableData> tableDataItemMap, String key) {
        for (Map.Entry<String, DomesticTableData> entry : tableDataItemMap.entrySet()) {
            String[] keyList = entry.getKey().split(":");
            for (String keyStr : keyList) {
                if (keyStr.equals(key)) {
                    return entry.getValue();
                }
            }
        }
        return null;
    }

    public static void buildBus567DomesticTableData(Integer actualGapDays, Integer metricId, List<Domestic567WeakNessBO> currentResult,
                                                    List<DomesticTableData> tableDataItemList,
                                                    String field, List<String> headerList) {


        Double cwTarget = new Double(0 / 100.0);
        Double fwTarget = new Double(1 / 100.0);
        Double lhvwTarget = new Double(1 / 100.0);
        Double lowTarget = new Double(3 / 100.0);

        for (Domestic567WeakNessBO bean : currentResult) {
            tableDataItemList.add(build567TableRow(actualGapDays, bean, metricId, headerList, cwTarget, "核心"));//NOSONAR
            tableDataItemList.add(build567TableRow(actualGapDays, bean, metricId, headerList, fwTarget, "聚焦"));//NOSONAR
            tableDataItemList.add(build567TableRow(actualGapDays, bean, metricId, headerList, lhvwTarget, "长尾高价值"));//NOSONAR
            tableDataItemList.add(build567TableRow(actualGapDays, bean, metricId, headerList, lowTarget, "长尾其他"));//NOSONAR
        }
    }

    private static DomesticTableData build567TableRow(Integer actualGapDays, Domestic567WeakNessBO bean, Integer metricId,
                                                      List<String> headerList, Double targetValue, String level) {
        DomesticTableData row = new DomesticTableData();
        build567CommonDomesticRow(row, bean, headerList);
        row.setTargetRate(targetValue);
        Double value = getValueByMetric(metricId, level, bean);
        row.setCompleteRate(value);
        if (actualGapDays != null && actualGapDays != 0) {
            row.setCompleteRate(value / actualGapDays);
        }
        row.setRatioLevel(level);
        row.setGapValue(row.getCompleteRate() - row.getTargetRate());
        return row;
    }

    public static Double getValueByMetric(Integer metricId, String level, Domestic567WeakNessBO bean) {
        switch (metricId) {
            case 7:
                switch (level) {
                    case "核心"://NOSONAR
                        return bean.getCwCommRate();
                    case "聚焦"://NOSONAR
                        return bean.getFwCommRate();
                    case "长尾高价值"://NOSONAR
                        return bean.getLhvwCommRate();
                    case "长尾其他"://NOSONAR
                        return bean.getLowCommRate();
                }
                break;
            case 6:
                switch (level) {
                    case "核心"://NOSONAR
                        return bean.getCwSaleunitRate();
                    case "聚焦"://NOSONAR
                        return bean.getFwSaleunitRate();
                    case "长尾高价值"://NOSONAR
                        return bean.getLhvwSaleunitRate();
                    case "长尾其他"://NOSONAR
                        return bean.getLowSaleunitRate();
                }
                break;
            case 5:
                switch (level) {
                    case "核心"://NOSONAR
                        return bean.getCwViewspotRate();
                    case "聚焦"://NOSONAR
                        return bean.getFwViewspotRate();
                    case "长尾高价值"://NOSONAR
                        return bean.getLhvwViewspotRate();
                    case "长尾其他"://NOSONAR
                        return bean.getLowViewspotRate();
                }
                break;
        }
        return new Double(0.0);
    }

    private static void build567CommonDomesticRow(DomesticTableData row,
                                                  Domestic567WeakNessBO bean,
                                                  List<String> headerList) {
        for (String header : headerList) {
            switch (header) {
                case "regionName":
                    row.setRegionName(bean.getBusinessRegionName());
                    break;
                case "provinceName":
                    row.setProvinceName(bean.getProvinceName());
                    break;
                case "examinee":
                    row.setExaminee(bean.getExamineObject());
                    row.setExamineeName(bean.getExamineName());
                    break;
            }
        }
    }

    private static String build567TableItemMapKey(String field,
                                                  Domestic567WeakNessBO bean) {
        //具体展示那几列是按照下钻维度区分的，所以这里key的维度也要按照展示几列来定
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "region_name":
                key.append(bean.getBusinessRegionName()).append(":");
                break;
            case "province_name":
                key.append(bean.getBusinessRegionName()).append(":");
                key.append(bean.getProvinceName()).append(":");
                break;
            case "examinee":
                key.append(bean.getBusinessRegionName()).append(":");
                key.append(bean.getProvinceName()).append(":");
                key.append(bean.getExamineObject()).append(":");
                break;
        }
        return key.toString();
    }

    public static void buildBus1And2DomesticTableData(List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult,
                                                      List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult,
                                                      List<BiGmvProfitTargetBO> targetResult,
                                                      List<String> tagList,
                                                      List<DomesticTableData> tableDataItemList,
                                                      String metric, String field, List<String> headerList) {
        Map<String, Double> lastYearValueMap = new HashMap<>();
        Double zero=new Double(0);
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : lastYearResult) {
            String key = buildDomesticTableMapKey(field, bean);
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            if (valueBean == null) {
                valueBean = zero;
            }
            if (lastYearValueMap.containsKey(key)) {
                Double value = lastYearValueMap.get(key);
                valueBean = valueBean + value;
                lastYearValueMap.put(key, valueBean);
            } else {

                lastYearValueMap.put(key, valueBean);
            }
        }
        Map<String, Double> targetValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(targetResult)) {
            for (BiGmvProfitTargetBO bean : targetResult) {
                StringBuilder key = new StringBuilder();
                switch (field) {
                    case "region_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        break;
                    case "province_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        break;
                    case "examinee":
                        key.append(bean.getExaminee());
                        break;
                    case "viewspotid":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        key.append(bean.getVstId()).append(":");
                        key.append(bean.getVstName()).append(":");
                        key.append(bean.getExaminee()).append(":");
                        break;
                }
                switch (metric) {
                    case "1":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtIncome().toString()));
                        break;
                    case "2":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtProfit().toString()));
                        break;
                }
            }
        }
        Map<String, DomesticTableData> currentMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : currentResult) {
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            String key = buildDomesticTableMapKey(field, bean);
            DomesticTableData row = null;
            if(currentMap.containsKey(key)){
                row = currentMap.get(key);
                //完成值累加、省份累加
                if (StringUtils.isNotEmpty(row.getProvinceName())) {
                    row.setProvinceName(String.format("%s、%s", row.getProvinceName(), bean.getBusinessSubRegionName()));
                } else {
                    row.setProvinceName(bean.getBusinessSubRegionName());
                }
                row.setCompleteValue(valueBean+row.getCompleteValue());
                if (lastYearValueMap.containsKey(key)) {
                    Double popValue = lastYearValueMap.get(key);
                    Double value = row.getCompleteValue();
                    if (GeneralUtil.isValidDivide(value, popValue)) {
                        row.setYoyValue(value / popValue - 1);
                    }
                }
                if (targetValueMap.containsKey(key)) {
                    Double targetValue = targetValueMap.get(key);
                    row.setTargetValue(targetValueMap.get(key));
                    row.setCompleteRate(targetValue == 0 ? targetValue : row.getCompleteValue() / row.getTargetValue());
                }
            }else {
                row = new DomesticTableData();
                buildCommonDomesticRow(row, bean, headerList);
                if ("2".equalsIgnoreCase(metric)) {
                    row.setInnerProfitValue(bean.getTtdSysInnerProfit());
                    row.setOuterProfitValue(bean.getTtdSysOuterProfit());
                }
                row.setCompleteValue(valueBean);
                row.setTargetValue(Double.valueOf(0));
                row.setYoyValue(Double.valueOf(0));
                row.setCompleteRate(Double.valueOf(0));
                if (lastYearValueMap.containsKey(key)) {
                    Double popValue  = lastYearValueMap.get(key);
                    Double value = row.getCompleteValue();
                    if (GeneralUtil.isValidDivide(value, popValue)) {
                        row.setYoyValue(value / popValue - 1);
                    }
                }
                if (targetValueMap.containsKey(key)) {
                    Double targetValue = targetValueMap.get(key);
                    row.setTargetValue(targetValueMap.get(key));
                    row.setCompleteRate(targetValue == 0 ? targetValue : row.getCompleteValue() / row.getTargetValue());
                }
                currentMap.put(key, row);
                tableDataItemList.add(row);
            }
        }
    }

    private static void buildCommonDomesticRow(DomesticTableData row,
                                               CdmOrdTtdDashboardExamLevelPerfDfBO bean,
                                               List<String> headerList) {
        for (String header : headerList) {
            switch (header) {
                case "regionName":
                    row.setRegionName(bean.getBusinessRegionName());
                    break;
                case "provinceName":
                    row.setProvinceName(bean.getBusinessSubRegionName());
                    break;
                case "examinee":
                    row.setExaminee(bean.getPrdMeid());
                    break;
                case "examineeName":
                    row.setExamineeName(bean.getPrdMeidName());
                    break;
                case "viewspotId":
                    row.setViewspotId(bean.getVstId().intValue());
                    break;
                case "viewspot":
                    row.setViewspot(bean.getVstName());
                    break;
            }
        }
    }

    private static String buildTableItemMapKey(String field,
                                               DomesticTableData bean) {
        //具体展示那几列是按照下钻维度区分的，所以这里key的维度也要按照展示几列来定
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "region_name":
                key.append(bean.getRegionName()).append(":");
                break;
            case "province_name":
                key.append(bean.getRegionName()).append(":");
                key.append(bean.getProvinceName()).append(":");
                break;
            case "examinee":
                key.append(bean.getRegionName()).append(":");
                key.append(bean.getProvinceName()).append(":");
                key.append(bean.getExaminee()).append(":");
                break;
            case "poi":
                key.append(bean.getRegionName()).append(":");
                key.append(bean.getProvinceName()).append(":");
                key.append(bean.getViewspotId()).append(":");
                key.append(bean.getViewspot()).append(":");
                key.append(bean.getExaminee()).append(":");
                break;

        }
        return key.toString();
    }

    private static String buildDomesticTableMapKey(String field,
                                                   CdmOrdTtdDashboardExamLevelPerfDfBO bean) {
        //具体展示那几列是按照下钻维度区分的，所以这里key的维度也要按照展示几列来定
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "region_name":
                key.append(bean.getBusinessRegionName()).append(":");
                break;
            case "province_name":
                key.append(bean.getBusinessRegionName()).append(":");
                key.append(bean.getBusinessSubRegionName()).append(":");
                break;
            case "examinee":
                key.append(bean.getPrdMeid());
                break;
            case "viewspotid":
                key.append(bean.getBusinessRegionName()).append(":");
                key.append(bean.getBusinessSubRegionName()).append(":");
                key.append(bean.getVstId()).append(":");
                key.append(bean.getVstName()).append(":");
                key.append(bean.getPrdMeid()).append(":");
                break;

        }
        return key.toString();
    }

    private static Double chooseValueByDomesticMetric(CdmOrdTtdDashboardExamLevelPerfDfBO bean, String metric) {
        if (bean == null) {
            return new Double(0);
        }
        switch (metric) {
            case "1":
                return bean.getTtdSucIncome();
            case "2":
                return bean.getTtdSucProfit();
        }
        return new Double(0);
    }


    //填充关联数据到普通表格数据中
    //适用于非初始化

    /***
     *
     * @param tableDataItemList  存储最后数据
     * @param matchList 关联数据
     * @param groupTagList 下钻相关字段
     * @param matchDimList 关联数据的指标
     */
    public static void fillOnlyMatchTableData(List<TableDataItem> tableDataItemList,
                                              List<String> groupTagList,
                                              List<String> matchDimList,
                                              List<List<Object>> matchList) {
        Map<String, TableDataItem> tableDataItemMap = new HashMap<>();
        for (TableDataItem item : tableDataItemList) {
            Map<String, String> fieldMap = item.getFieldMap();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < groupTagList.size(); i++) {
                String groupTag = groupTagList.get(i);
                String groupTagValue = fieldMap.get(groupTag);
                sb.append(groupTagValue).append(":");
            }
            tableDataItemMap.put(sb.toString(), item);
        }
        for (List<Object> rowResult : matchList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < groupTagList.size(); i++) {
                String groupTagValue = String.valueOf(rowResult.get(i));
                sb.append(groupTagValue).append(":");
            }
            TableDataItem item = tableDataItemMap.get(sb.toString());
            //由于是按进展排序 没有找到进展 直接跳过
            if (item == null) {
                continue;
            }
            Map<String, Double> dimMap = item.getDimMap();
            for (int i = groupTagList.size(); i < rowResult.size(); i++) {
                Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
                dimMap.put(matchDimList.get(i - groupTagList.size()), value);
            }
        }
    }


    //填充表格环比数据
    //所有填充的指标均为单指标  即不存在指标计算的形式

    /**
     * @param tableDataItemList 返回结果
     * @param rawResultList     同比原始数据
     * @param fieldList         下钻list
     * @param suffix            后缀
     * @param dimList           dim列表
     * @param virtualPreffix    虚拟后缀  用于处理同环比的可能情况
     */
    public static void fillTableSingleDimPopData(List<TableDataItem> tableDataItemList,
                                                 List<List<Object>> rawResultList,
                                                 List<String> fieldList,
                                                 String suffix,
                                                 List<String> dimList,
                                                 String virtualPreffix) {
        Map<String, List<TableDataItem>> itemListMap = new HashMap<>();
        for (TableDataItem item : tableDataItemList) {
            StringBuilder sb = new StringBuilder();
            for (String field : fieldList) {
                sb.append(item.getFieldMap().get(field)).append(":");
            }
            List<TableDataItem> originItemList = itemListMap.getOrDefault(sb.toString(), new ArrayList<>());
            originItemList.add(item);
            itemListMap.put(sb.toString(), originItemList);
//            itemMap.put(sb.toString(), item);
        }
        int size = fieldList.size();
        for (List<Object> rowResult : rawResultList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < size; i++) {
                sb.append(rowResult.get(i)).append(":");
            }
            List<TableDataItem> originItemList = itemListMap.get(sb.toString());
            if (GeneralUtil.isNotEmpty(originItemList)) {
                for (TableDataItem item : originItemList) {
                    for (int i = size; i < rowResult.size(); i++) {
                        String dim = dimList.get(i - size);
                        Object popRawValue = rowResult.get(i);
                        if (popRawValue != null) {
                            Double popValue = Double.valueOf(String.valueOf(popRawValue));
                            Double value = item.getDimMap().get(dim + virtualPreffix);
                            if (GeneralUtil.isValidDivide(value, popValue)) {
                                item.getDimMap().put(dim + suffix, value / popValue - 1);
                            }
                        }
                    }
                }
            }
        }
    }


    //填充表格环比数据
    //所有填充的指标均为复合指标

    /**
     * @param tableDataItemList 返回结果
     * @param rawResultList     同比原始数据
     * @param groupTagList      下钻field列表
     * @param suffix            后缀
     * @param dimList           原始dim list
     * @param complexDimList    复合dimList
     */
    public static void fillTableComplexDimPopData(List<TableDataItem> tableDataItemList,
                                                  List<List<Object>> rawResultList,
                                                  List<String> groupTagList,
                                                  List<String> dimList,
                                                  List<String> complexDimList,
                                                  String suffix) {
        int size = groupTagList.size();
        Map<String, Double> popMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        for (List<Object> rowResult : rawResultList) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < size; i++) {
                String groupTagValue = String.valueOf(rowResult.get(i));
                sb.append(groupTagValue).append(":");
            }
            String drillDown = sb.toString();
            drillDownSet.add(drillDown);
            for (int i = size; i < rowResult.size(); i++) {
                String dim = dimList.get(i - size);
                Object rawValue = rowResult.get(i);
                if (rawValue != null) {
                    Double value = Double.valueOf(String.valueOf(rawValue));
                    popMap.put(drillDown + dim, value);
                }
            }
        }
        for (String complexDim : complexDimList) {
            String[] dimArray = complexDim.split("\\|");
            for (String drillDown : drillDownSet) {
                Double leftnum = popMap.get(drillDown + dimArray[0]);
                Double rightnum = popMap.get(drillDown + dimArray[1]);
                Double result = GeneralUtil.getComplexResult(leftnum, rightnum, dimArray[2]);
                popMap.put(drillDown + complexDim, result);
            }
        }


        for (TableDataItem item : tableDataItemList) {
            Map<String, String> fieldMap = item.getFieldMap();
            Map<String, Double> dimMap = item.getDimMap();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < size; i++) {
                String groupTag = groupTagList.get(i);
                String groupTagValue = fieldMap.get(groupTag);
                sb.append(groupTagValue).append(":");
            }
            String drillDown = sb.toString();
            for (String complexDim : complexDimList) {
                Double fenzi = dimMap.get(complexDim);
                Double fenmu = popMap.get(drillDown + complexDim);
                if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                    dimMap.put(complexDim + suffix, fenzi / fenmu - 1);
                }
            }
        }
    }


    //填充特殊表格数据

    /***
     *
     * @param tableDataItemList   输出
     * @param rawResultList 输入
     * @param field 关联字段
     * @param dimList 指标名称
     */
    public static void fillSpecialTableData(List<TableDataItem> tableDataItemList,
                                            List<List<Object>> rawResultList,
                                            String field,
                                            List<String> dimList) {
        Map<String, List<TableDataItem>> itemListMap = new HashMap<>();
        for (TableDataItem item : tableDataItemList) {
            String fieldName = item.getFieldMap().get(field);
            List<TableDataItem> groupItemList = itemListMap.get(fieldName);
            if (groupItemList == null) {
                groupItemList = new ArrayList<>();
            }
            groupItemList.add(item);
            itemListMap.put(fieldName, groupItemList);
        }
        for (List<Object> rowResult : rawResultList) {
            String fieldName = String.valueOf(rowResult.get(0));
            List<TableDataItem> groupItemList = itemListMap.get(fieldName);
            if (!GeneralUtil.isEmpty(groupItemList)) {
                for (TableDataItem groupItem : groupItemList) {
                    Map<String, Double> dimMap = groupItem.getDimMap();
                    for (int i = 1; i < rowResult.size(); i++) {
                        Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
                        dimMap.put(dimList.get(i - 1), value);
                    }
                }
            }
        }
    }


    //填充下钻field数据

    /***
     *
     * @param field  下钻field
     * @param rawObjectList 原始数据
     * @param fieldDataItemList 输出field数据
     */
    public static void fillFieldDataItemList(String field,
                                             List<List<Object>> rawObjectList,
                                             List<FieldDataItem> fieldDataItemList) {
        FieldDataItem item = new FieldDataItem();
        fieldDataItemList.add(item);
        String name = MetricHelper.getDataBaseColumnName(field);
        item.setField(name);
        List<FieldValueItem> fieldValueItemList = new ArrayList<>();
        item.setFieldValueItemList(fieldValueItemList);
        for (List<Object> rowResult : rawObjectList) {
            FieldValueItem fieldValueItem = new FieldValueItem();
            String value = String.valueOf(rowResult.get(0));
            fieldValueItem.setValue(value);
            if ("viewspotid".equals(field)) {
                String relationValue = String.valueOf(rowResult.get(1));
                fieldValueItem.setRelationValue(relationValue);
            }
            fieldValueItemList.add(fieldValueItem);
        }
    }


    //填充下钻field数据

    /***
     *
     * @param rawObjectList 原始数据
     * @param fieldDataItem 输出field数据
     */
    public static void fillFieldDataItem(List<List<Object>> rawObjectList,
                                         FieldDataItem fieldDataItem) {
        List<FieldValueItem> fieldValueItemList = new ArrayList<>();
        fieldDataItem.setFieldValueItemList(fieldValueItemList);
        for (List<Object> rowResult : rawObjectList) {
            FieldValueItem fieldValueItem = new FieldValueItem();
            String value = String.valueOf(rowResult.get(0));
            fieldValueItem.setValue(value);
            String relationValue = String.valueOf(rowResult.get(1));
            fieldValueItem.setRelationValue(relationValue);
            fieldValueItemList.add(fieldValueItem);
        }
    }

    public static void fillFieldDataItemV2(String metric, List<List<Object>> rawObjectList,
                                           DilldownDim fieldDataItem) {
        fieldDataItem.setDimValueList(new ArrayList<>());
        for (List<Object> rowResult : rawObjectList) {
            DimValueType dimValueType = new DimValueType();
            if ("examinee".equalsIgnoreCase(fieldDataItem.getDimName())
                    && ("101".equalsIgnoreCase(metric) || "102".equalsIgnoreCase(metric) || "110".equalsIgnoreCase(metric))) {
                String value = String.valueOf(rowResult.get(1));
                String relationValue = String.valueOf(rowResult.get(0));
                dimValueType.setId(value);
                dimValueType.setDimValue(relationValue);
            } else {
                String value = String.valueOf(rowResult.get(0));
                String relationValue = String.valueOf(rowResult.get(1));
                dimValueType.setId(value);
                dimValueType.setDimValue(relationValue);
            }
            fieldDataItem.getDimValueList().add(dimValueType);
        }
    }


    //将关联信息添加到表格数据的field中
    //适用于gmv和毛利指标
    //将poi名称直接填充到对应的返回中

    /***
     *
     * @param rawResultList 存储原始数据
     * @param tableDataItemList 返回结果
     */
    public static void fillMatchInfoToFieldMap(List<List<Object>> rawResultList,
                                               List<TableDataItem> tableDataItemList) {

        Map<String, String> marchMap = new HashMap<>();
        for (List<Object> rowResult : rawResultList) {
            String id = String.valueOf(rowResult.get(0));
            String name = String.valueOf(rowResult.get(1));
            marchMap.put(id, name);
        }
        for (TableDataItem item : tableDataItemList) {
            Map<String, String> fieldMap = item.getFieldMap();
            String id = fieldMap.get("viewspotid");
            String name = marchMap.getOrDefault(id, "");
            fieldMap.put("viewspot_name", name);
        }
    }

    //填充排名数据
    public static void fillRankData(MetricDetailInfo metricDetailInfo,
                                    List<List<Object>> rawResultList) {
        if (rawResultList.size() > 0) {
            Object rankObject = rawResultList.get(0).get(0);
            if (rankObject != null) {
                metricDetailInfo.setRank(String.valueOf(rankObject));
            }
        }
    }

    //填充排名数据V2
    public static void fillRankDataV2(DomesticMetricDetailInfo metricDetailInfo,
                                      List<List<Object>> rawResultList) {
        if (!rawResultList.isEmpty()) {
            Object rankObject = rawResultList.get(0).get(0);
            if (rankObject != null) {
                metricDetailInfo.setRank(Integer.parseInt(String.valueOf(rankObject).split("/")[0]));
                metricDetailInfo.setTotalRank(Integer.parseInt(String.valueOf(rankObject).split("/")[1]));
            }
        }
    }

    public static void buildBus1And2DomesticFirstPageTableData(List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult,
                                                               List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult,
                                                               List<BiGmvProfitTargetBO> targetResult,
                                                               List<String> tagList,
                                                               String metric,
                                                               String field,
                                                               List<String> headerList,
                                                               List<FirstPageDomesticTableData> tableDataItemList) {
        Map<String, Double> lastYearValueMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : lastYearResult) {
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            String key = buildDomesticTableMapKey(field, bean);
            if (lastYearValueMap.containsKey(key)) {
                Double value = lastYearValueMap.get(key);
                value = valueBean + value;
                lastYearValueMap.put(key, value);
            } else {
                lastYearValueMap.put(key, valueBean);
            }
        }
        Map<String, Double> targetValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(targetResult)) {
            for (BiGmvProfitTargetBO bean : targetResult) {
                StringBuilder key = new StringBuilder();
                switch (field) {
                    case "region_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        break;
                    case "province_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        break;
                    case "examinee":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        key.append(bean.getExaminee()).append(":");
                        break;
                    case "viewspotid":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        key.append(bean.getVstId()).append(":");
                        key.append(bean.getVstName()).append(":");
                        key.append(bean.getExaminee()).append(":");
                        break;
                }
                switch (metric) {
                    case "1":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtIncome().toString()));
                        break;
                    case "2":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtProfit().toString()));
                        break;
                }
            }
        }
        Map<String, FirstPageDomesticTableData> currentMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : currentResult) {
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            String key = buildDomesticTableMapKey(field, bean);
            FirstPageDomesticTableData row = new FirstPageDomesticTableData();
            for (String header : headerList) {
                switch (header) {
                    case "regionName":
                        row.setRegionName(bean.getBusinessRegionName());
                        break;
                    case "provinceName":
                        row.setProvinceName(bean.getBusinessSubRegionName());
                        break;
                    case "examinee":
                        row.setExaminee(bean.getPrdMeid());
                        break;
                    case "viewspot":
                        row.setViewspotId(bean.getVstId().intValue());
                        row.setViewspot(bean.getVstName());
                        break;
                }
            }
            if (targetValueMap.containsKey(key)) {
                row.setCompleteRate(valueBean / targetValueMap.get(key));
            }
            Double popValue = lastYearValueMap.get(key);
            Double value = valueBean;
            if (GeneralUtil.isValidDivide(value, popValue)) {
                row.setYoyValue(value / popValue - 1);
            }
            currentMap.put(key, row);
            tableDataItemList.add(row);
        }
    }

    public static void buildBus1And2ButypeTableData(List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult,
                                                    List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult,
                                                    List<BiGmvProfitTargetBO> targetResult,
                                                    String buType,
                                                    List<DomesticTableData> tableDataItemList,
                                                    String metric,
                                                    String field,
                                                    List<String> tableHeaderList) {

        Double zero=new Double(0);
        Map<String, Double> lastYearValueMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : lastYearResult) {
            Double valueBean = chooseValueByDomesticMetric(bean, metric);
            String key = buildDomesticTableMapKey(field, bean);
            if (lastYearValueMap.containsKey(key)) {
                Double value = lastYearValueMap.get(key);
                valueBean = valueBean + value;
                lastYearValueMap.put(key, valueBean);
            } else {
                lastYearValueMap.put(key, valueBean);
            }
        }

        Map<String, Double> targetValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(targetResult)) {
            for (BiGmvProfitTargetBO bean : targetResult) {
                StringBuilder key = new StringBuilder();
                switch (field) {
                    case "region_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        break;
                    case "province_name":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        break;
                    case "examinee":
                        key.append(bean.getExaminee());
                        break;
                    case "viewspotid":
                        key.append(bean.getBusinessRegionName()).append(":");
                        key.append(bean.getBusinessSubRegionName()).append(":");
                        key.append(bean.getVstId()).append(":");
                        key.append(bean.getVstName()).append(":");
                        key.append(bean.getExaminee()).append(":");
                        break;
                }
                switch (metric) {
                    case "1":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtIncome().toString()));
                        break;
                    case "2":
                        targetValueMap.put(key.toString(), Double.valueOf(bean.getTtdTrgtProfit().toString()));
                        break;
                }
            }
        }
        Map<String, DomesticTableData> tableDataItemMap = new HashMap<>();
        for (DomesticTableData row : tableDataItemList) {
            String key = buildTableItemMapKey(field, row);
            tableDataItemMap.put(key, row);
        }
        Map<String, DomesticTableData> currentMap = new HashMap<>();
        for (CdmOrdTtdDashboardExamLevelPerfDfBO bean : currentResult) {
            String key = buildDomesticTableMapKey(field, bean);
            DomesticTableData row = null;
            if ("examinee".equalsIgnoreCase(field)) {
                row = getRowByKey(tableDataItemMap, key);
            } else {
                row = tableDataItemMap.get(key);
            }
            if (row == null) {
                continue;
            }
            if (currentMap.containsKey(key)) {
                //完成值累加、省份累加
                Double popValue = lastYearValueMap.get(key);
                Double valueBean = chooseValueByDomesticMetric(bean, metric);
                if ("act".equalsIgnoreCase(buType)) {
                    row.setActProvinceName(String.format("%s、%s", row.getActProvinceName(), bean.getBusinessSubRegionName()));
                    row.setActCompleteValue(valueBean + row.getActCompleteValue());
                    if (GeneralUtil.isValidDivide(valueBean, popValue)) {
                        row.setActYoyValue(valueBean / popValue - 1);
                    }
                    if (targetValueMap.containsKey(key)) {
                        row.setActTargetValue(targetValueMap.get(key));
                        row.setActCompleteRate(row.getActCompleteValue() / row.getActTargetValue());
                    }
                    if ("2".equalsIgnoreCase(metric)) {
                        row.setActInnerProfitValue(row.getActInnerProfitValue() + bean.getTtdSysInnerProfit());
                        row.setActOuterProfitValue(row.getActOuterProfitValue() + bean.getTtdSysOuterProfit());
                    }
                } else {
                    row.setOdfProvinceName(String.format("%s、%s", row.getOdfProvinceName(), bean.getBusinessSubRegionName()));
                    row.setOdfCompleteValue(row.getOdfCompleteValue() + valueBean);
                    if (GeneralUtil.isValidDivide(valueBean, popValue)) {
                        row.setOdfYoyValue(valueBean / popValue - 1);
                    }
                    if (targetValueMap.containsKey(key)) {
                        row.setOdfTargetValue(targetValueMap.get(key));
                        row.setOdfCompleteRate(row.getOdfCompleteValue() / row.getOdfTargetValue());
                    }
                    if ("2".equalsIgnoreCase(metric)) {
                        row.setOdfInnerProfitValue(row.getOdfInnerProfitValue() + bean.getTtdSysInnerProfit());
                        row.setOdfOuterProfitValue(row.getOdfOuterProfitValue() + bean.getTtdSysOuterProfit());
                    }
                }

            }else {
                //根据已有完成率填充现有业务线数据
                currentMap.put(key, row);
                Double popValue = lastYearValueMap.get(key);
                Double valueBean = chooseValueByDomesticMetric(bean, metric);
                if ("act".equalsIgnoreCase(buType)) {
                    row.setActProvinceName(bean.getBusinessSubRegionName());
                    row.setActCompleteValue(valueBean);
                    if (GeneralUtil.isValidDivide(valueBean, popValue)) {
                        row.setActYoyValue(valueBean / popValue - 1);
                    }
                    row.setActTargetValue(zero);
                    if (targetValueMap.containsKey(key)) {
                        row.setActTargetValue(targetValueMap.get(key));
                        if (!zero.equals(row.getActTargetValue())) {
                            row.setActCompleteRate(row.getActCompleteValue() / row.getActTargetValue());
                        }
                    }
                    if ("2".equalsIgnoreCase(metric)) {
                        row.setActInnerProfitValue(bean.getTtdSysInnerProfit());
                        row.setActOuterProfitValue(bean.getTtdSysOuterProfit());
                    }
                } else {
                    row.setOdfProvinceName(bean.getBusinessSubRegionName());
                    if (GeneralUtil.isValidDivide(valueBean, popValue)) {
                        row.setOdfYoyValue(valueBean / popValue - 1);
                    }
                    row.setOdfCompleteValue(valueBean);
                    row.setOdfTargetValue(zero);
                    if (targetValueMap.containsKey(key)) {
                        row.setOdfTargetValue(targetValueMap.get(key));
                        if (!zero.equals(row.getOdfTargetValue())) {
                            row.setOdfCompleteRate(row.getOdfCompleteValue() / row.getOdfTargetValue());
                        }
                    }
                    if ("2".equalsIgnoreCase(metric)) {
                        row.setOdfInnerProfitValue(bean.getTtdSysInnerProfit());
                        row.setOdfOuterProfitValue(bean.getTtdSysOuterProfit());
                    }
                }
            }

        }

    }
}
